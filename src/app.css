@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .scrollbar::-webkit-scrollbar {
    width: 10px; /* Size of vertical scrollbar */
    height: 10px; /* Size of horizontal scrollbar */
  }

  .scrollbar::-webkit-scrollbar-track {
    border-radius: 100vh;
    background: none;
  }

  .scrollbar::-webkit-scrollbar-thumb {
    background: #e5e7eb;
    border-radius: 100vh;
    border: 4px solid #ffffff;
  }

  .scrollbar::-webkit-scrollbar-thumb:hover {
    background: #d1d5db;
  }

  .dark .scrollbar::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 100vh;
    border: 4px solid #262626;
  }

  .dark .scrollbar::-webkit-scrollbar-thumb:hover {
    background: #525252;
  }
}

.flatpickr-calendar .flatpickr-day.selected,
.flatpickr-calendar .flatpickr-day.startRange,
.flatpickr-calendar .flatpickr-day.endRange,
.flatpickr-calendar .flatpickr-day.selected.inRange,
.flatpickr-calendar .flatpickr-day.startRange.inRange,
.flatpickr-calendar .flatpickr-day.endRange.inRange,
.flatpickr-calendar .flatpickr-day.selected:focus,
.flatpickr-calendar .flatpickr-day.startRange:focus,
.flatpickr-calendar .flatpickr-day.endRange:focus,
.flatpickr-calendar .flatpickr-day.selected:hover,
.flatpickr-calendar .flatpickr-day.startRange:hover,
.flatpickr-calendar .flatpickr-day.endRange:hover,
.flatpickr-calendar .flatpickr-day.selected.prevMonthDay,
.flatpickr-calendar .flatpickr-day.startRange.prevMonthDay,
.flatpickr-calendar .flatpickr-day.endRange.prevMonthDay,
.flatpickr-calendar .flatpickr-day.selected.nextMonthDay,
.flatpickr-calendar .flatpickr-day.startRange.nextMonthDay,
.flatpickr-calendar .flatpickr-day.endRange.nextMonthDay {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  background: #111 !important;
  color: #fff !important;
  border-color: #111 !important;
}

.flatpickr-calendar {
  width: 350px !important;
  padding: 20px !important;
  background: white !important;
  border-radius: 20px !important;
}

.flatpickr-days {
  border: 0px !important;
}

.flatpickr-calendar .flatpickr-months .flatpickr-month {
  background: #fff !important;
  color: #111 !important;
  border-color: #fff !important;
}

.flatpickr-calendar .flatpickr-months .flatpickr-prev-month,
.flatpickr-calendar .flatpickr-months .flatpickr-next-month {
  fill: #111 !important;
  top: 20px;
}

.flatpickr-calendar .flatpickr-current-month .flatpickr-monthDropdown-months {
  background: #fff !important;
  color: #111 !important;
  border-color: #fff !important;
  text-align: center;
}

.flatpickr-calendar .flatpickr-weekdays {
  background: #fff !important;
  color: #111 !important;
  margin: 10px 0 !important;
}

.flatpickr-calendar .flatpickr-weekday {
  background: #fff !important;
  color: #111 !important;
}

.flatpickr-calendar .flatpickr-innerContainer {
  padding: 20px 0 !important;
}

.flatpickr-calendar .numInputWrapper:hover,
.numInputWrapper.span:hover {
  background: #fff !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months:focus {
  outline: none !important;
  border: 0px !important;
}

.flatpickr-day {
  margin: 10px 0 !important;
}

.flatpickr-day.endRange {
  -webkit-box-shadow: -10px 0 0 #111 !important;
  box-shadow: -10px 0 0 #111 !important;
}
.flatpickr-day.inRange {
  -webkit-box-shadow: -5px 0 0 #ffedd5 !important;
  box-shadow: -5px 0 0 #ffedd5 !important;
  background: #ffedd5 !important;
  border-color: #ffedd5 !important;
}
.remove-arrow::-webkit-inner-spin-button,
.remove-arrow::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.remove-arrow {
  -moz-appearance: textfield;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
/* For IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
