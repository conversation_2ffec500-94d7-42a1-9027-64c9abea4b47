<script lang="ts">
  import { onMount } from "svelte";

  import { goto } from "$app/navigation";
  import {
    failureToast,
    successToast,
  } from "$lib/components/ui/notifications/toast";
  import { request } from "$lib/services/api-caller/request";
  import { authStore } from "$lib/stores/auth-store";
  import { type RequestError } from "$lib/utils/errors/request-error";

  onMount(async () => {
    try {
      await request.delete<undefined>("/session", {});
      successToast("You have been logged out successfully.");
      authStore.set({ isLoggedIn: false });
      await goto("/login");
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(
        requestError?.message ??
          "We are having trouble logging you out. Please try again shortly."
      );
    }
  });
</script>
