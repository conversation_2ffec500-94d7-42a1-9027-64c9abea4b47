<script lang="ts">
  import { afterUpdate } from "svelte";

  import { goto } from "$app/navigation";
  import { authStore } from "$lib/stores/auth-store";

  $: ({ userProfile: user, isLoggedIn } = $authStore);

  afterUpdate(async () => {
    if (isLoggedIn && user) {
      await goto("/");
    }
  });
</script>

<div
  class="flex h-full w-full justify-center from-white
 via-blue-100 via-50% to-blue-50 lg:bg-gradient-to-r"
>
  <slot />
</div>
