<script lang="ts">
  import { onMount } from "svelte";

  import { PUBLIC_GITHUB_SHA } from "$env/static/public";
  import logoHorizontal from "$lib/assets/horizontal-logo.png";
  import logoSquare from "$lib/assets/square-logo.png";
  import LoginForm from "$lib/components/login/LoginForm.svelte";
  import { request } from "$lib/services/api-caller/request";

  const gitHashFrontend = (PUBLIC_GITHUB_SHA as string) || "unavailable";
  let gitHashBackend = "unavailable";

  onMount(async () => {
    try {
      const gitHashBackendResult = await request.get<{ gitHash: string }>(
        "/build"
      );
      gitHashBackend = gitHashBackendResult?.gitHash || "unavailable";
    } catch (error) {
      console.error("Error getting backend build:", error);
    }
  });
</script>

<div
  class="flex min-h-full w-full relative overflow-hidden"
  role="presentation"
>
  <div
    class="flex flex-1 flex-col justify-center drop-shadow-2xl px-4 py-12
       bg-gradient-to-br from-white from-30% to-white/60
       backdrop-blur-xl lg:px-6 lg:flex-none xl:px-24 z-50"
  >
    <div class="mx-auto w-full max-w-2xl lg:w-[100rem] flex flex-col">
      <img
        class="w-72 absolute left-24 top-16 max-lg:hidden"
        src={logoHorizontal}
        alt="Gigadat Global"
      />
      <img
        class="w-16 lg:hidden self-center pb-4"
        src={logoSquare}
        alt="Gigadat Global"
      />
      <div>
        <h2
          class=" text-2xl font-bold tracking-tight text-gray-800
           max-lg:text-center pb-2"
        >
          Login To FA
        </h2>
        <p class="max-lg:text-center">
          Welcome back! Please login to your account
        </p>
        <LoginForm />
      </div>
    </div>
  </div>

  <div
    class="w-full h-full hidden lg:flex justify-center items-center
     relative bg-blue-600"
  >
    <p
      class="text-[4rem] leading-[4rem] font-semibold text-white
       text-end absolute right-24 top-16 xl:w-[40rem] z-40"
    >
      The tool designed to automate financial needs.
    </p>

    <!--Smallest Largest Square-->
    <div
      class="rounded-[5rem] 2xl:rounded-[8rem] z-20 h-[28rem] w-[28rem]
       2xl:h-[52rem] 2xl:w-[52rem] absolute -left-[1rem] 2xl:left-[11.2rem]
        2xl:-bottom-[45rem] -bottom-[25rem] rotate-45 bg-blue-300"
    ></div>
    <!--Second Largest Square-->
    <div
      class="rounded-[8rem] 2xl:rounded-[14rem] z-10 h-[36rem] 2xl:h-[72rem]
       2xl:w-[72rem] w-[36rem] absolute -left-[5rem] -bottom-[24rem]
        2xl:-bottom-[48rem] 2xl:left-[1.5rem] rotate-45 bg-blue-600"
    ></div>

    <!--Largest Square-->
    <div
      class="rounded-[12rem] 2xl:rounded-[22rem] h-[50rem] w-[50rem]
       2xl:h-[100rem] 2xl:w-[100rem] absolute -left-[12rem] -bottom-[30rem]
        2xl:-bottom-[60rem] rotate-45 bg-blue-400"
    ></div>
    <!--Dollar Icon-->
    <div
      class=" absolute right-48 top-[40rem] h-36 w-36 bg-blue-200
       rounded-full flex items-center justify-center outline
        outline-[1rem] outline-blue-500 z-40"
    >
      <i class="bi bi-currency-dollar text-blue-500 text-[4rem]"></i>
    </div>

    <div
      class="z-50 flex gap-4 absolute -rotate-90 w-96 -right-40 bottom-48
       text-lg text-white"
    >
      <p>
        Build FE: {gitHashFrontend}
      </p>
      <p>
        Build BE: {gitHashBackend}
      </p>
    </div>
    <div
      class="border-r z-50 border-white absolute right-16 top-0 h-full"
    ></div>
  </div>
</div>
