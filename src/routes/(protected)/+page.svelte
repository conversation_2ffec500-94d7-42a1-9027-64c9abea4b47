<script lang="ts">
  import { PUBLIC_API_URL } from "$env/static/public";
  import HomepageCard from "$lib/components/ui/homepage/HomepageCard.svelte";

  let otherHovered = false;
</script>

<div class="relative overflow-y-auto flex flex-col gap-4 h-screen w-full p-6">
  <HomepageCard
    title="Store Client Data"
    subtitle="Manage all client information and history"
    bind:otherHovered
    link="/users"
  />
  <HomepageCard
    title="Create Settlements"
    subtitle="Generate settlement statements for all clients"
    link="/settlement"
    bind:otherHovered
  />
  <HomepageCard
    title="Email Notifications"
    subtitle="Email clients regarding payment updates"
    link="/email"
    bind:otherHovered
  />
  <HomepageCard
    title="Documentation"
    subtitle="Learn how to use FA here"
    link="{PUBLIC_API_URL}/documentation"
    bind:otherHovered
  />
</div>
