<script lang="ts">
  import { page } from "$app/stores";
  import AdjustmentsModal from "$lib/components/settlement/modals/AdjustmentsModal.svelte";
  import SettlementCard from "$lib/components/settlement/SettlementCard.svelte";
  import SettlementAdjustmentsTable from "$lib/components/settlement/tables/SettlementAdjustmentsTable.svelte";
  import SettlementDetailsTable from "$lib/components/settlement/tables/SettlementsDetailsTable.svelte";
  import ButtonGroup from "$lib/components/ui/buttons/ButtonGroup.svelte";
  import Button from "$lib/components/ui/buttons/ButtonNew.svelte";
  import DropDownSelector from "$lib/components/ui/drop-down/DropDownSelector.svelte";
  import IconOrText from "$lib/components/ui/table/common-cell-components/IconOrText.svelte";
  import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";

  import type { ButtonConfig } from "$lib/components/ui/buttons/types/button";
  import type { Head<PERSON>, Row } from "$lib/components/ui/table/types";
  import type { SettlementSummary } from "$lib/types/settlement/types";

  const settlementId = $page.params.id;
  let showModal = false;
  let selectedDetailsRow = new Set<number>();
  let selectedAdjustmentRow = new Set<number>();

  let detailsTableRows: Row[] = [];
  let adjustmentsTableRows: Row[] = [];

  const buttons: ButtonConfig[] = [
    {
      label: "Approve",
      onClick() {
        console.log("Approve clicked");
      },
      intent: "outline",
      color: "green",
    },
    {
      label: "Regenerate",
      onClick() {
        console.log("Reject clicked");
      },
      intent: "outline",
      color: "blue",
    },
    {
      label: "Delete",
      onClick() {
        console.log("Details clicked");
      },
      intent: "outline",
      color: "red",
    },
  ];
  const platforms = [
    { label: "Summary", value: "summary" },
    { label: "IDP", value: "idp" },
    { label: "ETI", value: "eti" },
    { label: "ETF", value: "etf" },
    { label: "RFM", value: "rfm" },
    { label: "ETO", value: "eto" },
    { label: "RTO", value: "rto" },
    { label: "RTX", value: "rtx" },
    { label: "ACH", value: "ach" },
    { label: "ANR", value: "anr" },
    { label: "ANX", value: "anx" },
    { label: "KYC", value: "kyc" },
  ];
  type Platform = {
    label: string;
    value: string;
  };

  let selectedPlatform = platforms[0];
  const headers: Header[] = [
    { id: "information", title: "Information", align: "left", sortable: false },
    {
      id: "numOfTransactions",
      title: "# of Transactions",
      align: "left",
      sortable: false,
    },
    {
      id: "transactionValue",
      title: "Transaction Value",
      align: "left",
      sortable: false,
    },
    {
      id: "accountPayableReceivable",
      title: "Account Payable / (Receivable)",
      align: "left",
      sortable: false,
    },
  ];
  const adjustmentsHeaders: Header[] = [
    {
      id: "adjustmentLabel",
      title: "Adjustment Label",
      align: "left",
      sortable: false,
    },
    {
      id: "value",
      title: "Value",
      align: "left",
      sortable: false,
    },
    {
      id: "comment",
      title: "Comment",
      align: "left",
      sortable: false,
    },
    {
      id: "displayOnExcel",
      title: "Display on Excel",
      align: "left",
      sortable: false,
    },
    {
      id: "actions",
      title: "Actions",
      align: "left",
      sortable: false,
    },
  ];

  const settlementData: SettlementSummary[] = [
    {
      id: "329102",
      customerName: "180 Group",
      serviceNumber: "**********",
      customerType: "Merchant",
      status: "Processing",
      netPayout: "**********",
      endBalance: "**********",
      fromDate: "2021-01-01",
      toDate: "2021-01-31",
      /* eslint-disable @typescript-eslint/naming-convention */
      platformSettlements: {
        ETF: { isAdjusted: true },
        IDP: { isAdjusted: false },
        ETI: { isAdjusted: false },
      },
    },
    // Other settlements...
  ];

  const settlement = settlementData.find(
    (settlement) => settlement.id === settlementId
  );

  const detailsMockData = [
    {
      information: "Interac Online Pay-In (IDP)",
      numOfTransactions: 120,
      transactionValue: "$12,000",
      accountPayableReceivable: "$1,200",
    },
    {
      information: "Interac e-Transfer Pay-In (ETI)",
      numOfTransactions: 95,
      transactionValue: "$9,500",
      accountPayableReceivable: "$950",
    },
    {
      information: "Interac e-Transfer Request Money (RFM)",
      numOfTransactions: 200,
      transactionValue: "$15,000",
      accountPayableReceivable: "$1,500",
    },
    {
      information: "Interac e-Transfer Pay-Out (ETO)",
      numOfTransactions: 150,
      transactionValue: "$10,000",
      accountPayableReceivable: "$1,000",
    },
    {
      information: "Real-time Interac e-Transfer Pay-Out (RTO)",
      numOfTransactions: 300,
      transactionValue: "$20,000",
      accountPayableReceivable: "$2,000",
    },
    {
      information: "Real-time Interac e-Transfer Pay-Out (RTX)",
      numOfTransactions: 250,
      transactionValue: "$18,000",
      accountPayableReceivable: "$1,800",
    },
    {
      information: "eCashout Pay-Outs (ACH)",
      numOfTransactions: 180,
      transactionValue: "$14,000",
      accountPayableReceivable: "$1,400",
    },
    {
      information: "Real-time eCashout (ANR)",
      numOfTransactions: 220,
      transactionValue: "$16,000",
      accountPayableReceivable: "$1,600",
    },
    {
      information: "Real-time eCashout (ANX)",
      numOfTransactions: 190,
      transactionValue: "$13,000",
      accountPayableReceivable: "$1,300",
    },
  ];

  const adjustmentsMockData = [
    {
      adjustmentLabel: "Adjustment Label",
      value: "$128,526.98",
      comment: "-",
      displayOnExcel: "True",
      actions: "X",
    },
    {
      adjustmentLabel: "Adjustment Label",
      value: "$400.68",
      comment: "-",
      displayOnExcel: "True",
      actions: "X",
    },
  ];

  function handleAddAdjustment() {
    showModal = true;
  }
  const getDetailsTableRows = (data: typeof detailsMockData): Row[] => {
    return data.map((item, index) => ({
      id: index.toString(),
      showCheckbox: false,
      cells: [
        {
          component: ListText,
          props: {
            lines: [[{ text: item.information, classes: " dark:text-white" }]],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [
                {
                  text: item.numOfTransactions.toString(),
                  classes: " dark:text-white",
                },
              ],
            ],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [{ text: item.transactionValue, classes: " dark:text-white" }],
            ],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [
                {
                  text: item.accountPayableReceivable,
                  classes: " dark:text-white",
                },
              ],
            ],
          },
        },
      ],
    }));
  };

  const getAdjustmentsTableRows = (data: typeof adjustmentsMockData): Row[] => {
    return data.map((item, index) => ({
      id: index.toString(),
      showCheckbox: false,
      cells: [
        {
          component: ListText,
          props: {
            lines: [
              [
                {
                  text: item.adjustmentLabel,
                  classes: "text-primary-500 dark:text-white",
                },
              ],
            ],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [{ text: item.value.toString(), classes: "text-primary-500" }],
            ],
          },
        },
        {
          component: ListText,
          props: {
            lines: [[{ text: item.comment, classes: "text-primary-500" }]],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [{ text: item.displayOnExcel, classes: "text-primary-500" }],
            ],
          },
        },
        {
          component: IconOrText,
          props: {
            right: {
              text: "bi bi-x",
              isIcon: true,
              classes: "text-2xl text-red-500",
            },
          },
        },
      ],
    }));
  };

  detailsTableRows = getDetailsTableRows(detailsMockData);
  adjustmentsTableRows = getAdjustmentsTableRows(adjustmentsMockData);

  async function handleTableRowClick(event: CustomEvent<{ rowId: number }>) {
    const { rowId } = event.detail;
    console.log("Row clicked:", rowId);
  }

  async function handleAdjustmentsTableRowClick(
    event: CustomEvent<{ rowId: number }>
  ) {
    const { rowId } = event.detail;
    console.log("Row clicked:", rowId);
  }

  function handlePlatformChange(event: CustomEvent<Platform>) {
    selectedPlatform = event.detail; // Update the selected platform
    console.log("Selected Platform:", selectedPlatform);
  }
</script>

<div
  class="flex w-full flex-col justify-between overflow-y-scroll h-full dark:bg-black"
>
  {#if settlement}
    <div class="flex flex-col gap-3 p-4 w-full overflow-y-scroll">
      <SettlementCard
        id={settlementId}
        {settlement}
        on:checkboxToggle={() => {
          console.log("checkboxToggle");
        }}
        checked={true}
        hasCheckbox={false}
      />
      <div class="flex flex-row items-end gap-4">
        <div class="flex-1">
          <DropDownSelector
            id="generate-settlement-modal-dropdown"
            clearable={false}
            label="Selected Platform"
            labelText={selectedPlatform.label}
            bind:value={selectedPlatform.value}
            items={platforms}
            placeholder="Select Frequency"
            on:change={handlePlatformChange}
          />
        </div>
        <Button
          on:click={handleAddAdjustment}
          color="green"
          className="rounded-md  h-[40px] gap-1 w-44"
        >
          <p class="text-xs px-1">Add Adjustment</p>
        </Button>
      </div>
      <SettlementDetailsTable
        {headers}
        rows={detailsTableRows}
        bind:selectedRows={selectedDetailsRow}
        onRowClick={handleTableRowClick}
      />
      <SettlementAdjustmentsTable
        headers={adjustmentsHeaders}
        rows={adjustmentsTableRows}
        bind:selectedRows={selectedAdjustmentRow}
        onRowClick={handleAdjustmentsTableRowClick}
      />
    </div>
    <div>
      <div
        class=" px-4 py-2 flex items-center dark:text-white justify-end gap-4"
      >
        <div>
          <span class="font-medium">Total Payout</span>
          <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
            -
          </div>
        </div>
        <div>
          <span class="font-medium">Total Adjustments</span>
          <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
            -
          </div>
        </div>
        <div>
          <span class="font-medium">Net Payout</span>
          <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
            -
          </div>
        </div>
        <div>
          <span class="font-medium">End Balance</span>
          <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
            -
          </div>
        </div>
      </div>
      <div
        class="border-t px-4 py-2 flex items-center justify-between dark:border-neutral-600"
      >
        <div>
          <Button
            className="rounded-full"
            intent="solid"
            color="white"
            on:click={() => {
              window.history.back();
            }}>Back</Button
          >
        </div>

        <div class="flex items-center gap-4">
          <Button
            intent="solid"
            color="white"
            on:click={() => {
              console.log("Visit Email");
            }}>Visit Email</Button
          >
          <ButtonGroup {buttons} />
        </div>
      </div>
    </div>
    <AdjustmentsModal bind:showModal />
  {:else}
    <p class="dark:text-white">No settlement found for ID: {settlementId}</p>
  {/if}
</div>
