<script lang="ts">
  import { onMount } from "svelte";

  import { goto } from "$app/navigation";
  import { page } from "$app/stores";
  import AdjustmentsModal from "$lib/components/settlement/modals/AdjustmentsModal.svelte";
  import DeleteSettlementsModal from "$lib/components/settlement/modals/DeleteSettlementsModal.svelte";
  import SettlementCard from "$lib/components/settlement/SettlementCard.svelte";
  import SettlementDetailsFooter from "$lib/components/settlement/SettlementDetailsFooter.svelte";
  import SettlementAdjustmentsTable from "$lib/components/settlement/tables/SettlementAdjustmentsTable.svelte";
  import SettlementDetailsTable from "$lib/components/settlement/tables/SettlementDetailsTable.svelte";
  import Button from "$lib/components/ui/buttons/ButtonNew.svelte";
  import DropDownSelector from "$lib/components/ui/drop-down/DropDownSelector.svelte";
  import {
    failureToast,
    infoToast,
  } from "$lib/components/ui/notifications/toast";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import IconOrText from "$lib/components/ui/table/common-cell-components/IconOrText.svelte";
  import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
  import { request } from "$lib/services/api-caller/request";
  import {
    platformCodes,
    type PlatformCode,
    type SettlementSummary,
  } from "$lib/types/settlement/types";
  import { formatCurrency } from "$lib/utils/format-currency";

  import type { Header, Row } from "$lib/components/ui/table/types";
  import type { RequestError } from "$lib/utils/errors/request-error";

  const settlementId = $page.params.id;

  let settlement: SettlementSummary;

  let showAdjustmentsModal = false;
  let showDeleteModal = false;
  let isLoading = false;

  let isDeleting = false;

  let failedToDeleteSettlements: Array<{
    settlementId: string;
    customerName: string;
    serviceNumber: string;
    fromDate: string;
    toDate: string;
  }> = [];

  // Resetting failedToDeleteSettlements when the modal is closed
  $: if (!showDeleteModal) {
    failedToDeleteSettlements = [];
  }

  let isValidPlatform = false;

  let adjustmentsTableRows: Row[] = [];

  type Platform = {
    label: string;
    value: PlatformCode;
  };
  const platforms: Platform[] = [{ label: "Summary", value: "SUMMARY" }];

  let selectedPlatform = { ...platforms[0] };

  $: selectedPlatform.value = $page.url.searchParams.get(
    "platform"
  ) as PlatformCode;

  const adjustmentsHeaders: Header[] = [
    {
      id: "adjustmentLabel",
      title: "Adjustment Label",
      align: "left",
      sortable: false,
    },
    {
      id: "value",
      title: "Value",
      align: "left",
      sortable: false,
    },
    {
      id: "comment",
      title: "Comment",
      align: "left",
      sortable: false,
    },
    {
      id: "displayOnExcel",
      title: "Display on Excel",
      align: "left",
      sortable: false,
    },
    {
      id: "actions",
      title: "Actions",
      align: "left",
      sortable: false,
    },
  ];

  const adjustmentsMockData = [
    {
      adjustmentLabel: "Adjustment Label",
      value: "128526.98",
      comment: "",
      displayOnExcel: "True",
      actions: "X",
    },
    {
      adjustmentLabel: "Adjustment Label",
      value: "400.68",
      comment: "",
      displayOnExcel: "True",
      actions: "X",
    },
  ];

  function handleAddAdjustment() {
    showAdjustmentsModal = true;
  }

  function addAdjustment(
    event: CustomEvent<{
      label: string;
      amount: string;
      displayOnExcel: string;
      comment: string;
    }>
  ) {
    const { label, amount, displayOnExcel, comment } = event.detail;
    adjustmentsMockData.push({
      adjustmentLabel: label,
      value: amount,
      comment,
      displayOnExcel,
      actions: "X",
    });

    adjustmentsTableRows = getAdjustmentsTableRows(adjustmentsMockData);
  }

  const getAdjustmentsTableRows = (data: typeof adjustmentsMockData): Row[] => {
    return data.map((item, index) => ({
      id: index.toString(),
      showCheckbox: false,
      cells: [
        {
          component: ListText,
          props: {
            lines: [
              [
                {
                  text: item.adjustmentLabel,
                  classes: "text-primary-500 dark:text-white",
                },
              ],
            ],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [
                {
                  text: formatCurrency(item.value),
                  classes: "text-primary-500",
                },
              ],
            ],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [{ text: item.comment || "-", classes: "text-primary-500" }],
            ],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [{ text: item.displayOnExcel, classes: "text-primary-500" }],
            ],
          },
        },
        {
          component: IconOrText,
          props: {
            right: {
              text: "bi bi-x",
              isIcon: true,
              classes: "text-2xl text-red-500",
            },
          },
        },
      ],
    }));
  };

  adjustmentsTableRows = getAdjustmentsTableRows(adjustmentsMockData);

  const handlePlatformChange = async (event: CustomEvent<Platform>) => {
    selectedPlatform = event.detail;

    await goto(`${$page.url.pathname}?platform=${selectedPlatform.value}`, {
      replaceState: false,
      noScroll: true,
      keepFocus: true,
    });
  };

  const handleDelete = async () => {
    try {
      isDeleting = true;

      const response = await request.delete<{
        success: boolean;
        failedSettlementIds?: number[];
      }>("/settlement", {
        settlementIds: [settlementId],
      });

      if (response.success) {
        infoToast("Settlement deleted successfully.");

        showDeleteModal = false;

        await goto("/settlement");
      } else {
        if (!response.failedSettlementIds) {
          failureToast(
            "Failed to delete settlement, but no settlement IDs were returned."
          );
          showDeleteModal = false;

          return;
        }

        failedToDeleteSettlements.push({
          settlementId,
          customerName: settlement?.customerName ?? "Unknown",
          serviceNumber: settlement?.serviceNumber ?? "Unknown",
          fromDate: settlement?.fromDate ?? "Unknown",
          toDate: settlement?.toDate ?? "Unknown",
        });
      }
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(`Failed to delete settlement ${requestError.message}`);

      showDeleteModal = false;
    } finally {
      isDeleting = false;
    }
  };

  const openDeleteModal = () => {
    showDeleteModal = true;
  };

  const fetchSettlementData = async () => {
    isLoading = true;

    try {
      settlement = await request.get<SettlementSummary>(
        `/settlement/${settlementId}`
      );

      // Update dropdown options based on fetched settlement data
      for (const platformCode of Object.keys(
        settlement.platformSettlements
      ) as PlatformCode[]) {
        if (platformCode !== "SUMMARY") {
          platforms.push({
            label: platformCode,
            value: platformCode,
          });
        }
      }
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(`Failed to fetch settlement data: ${requestError.message}`);
    } finally {
      isLoading = false;
    }
  };

  const checkPlatformCode = (
    code: string | undefined
  ): code is PlatformCode => {
    return Boolean(code) && platformCodes.includes(code as PlatformCode);
  };

  onMount(async () => {
    await fetchSettlementData();

    // Ensure the platform query parameter is valid
    // Defaults to SUMMARY if not provided or invalid
    const parameter = $page.url.searchParams.get("platform") ?? undefined;

    if (parameter === undefined) {
      await goto(`${$page.url.pathname}?platform=SUMMARY`, {
        replaceState: true,
      });

      isValidPlatform = true;

      return;
    }

    isValidPlatform =
      checkPlatformCode(parameter) &&
      parameter in settlement.platformSettlements;

    if (!isValidPlatform) {
      failureToast("The page you are trying to access does not exist.");

      await goto(`${$page.url.pathname}?platform=SUMMARY`, {
        replaceState: true,
      });

      isValidPlatform = true;
    }
  });
</script>

<div class="flex w-full flex-col justify-between h-full dark:bg-black">
  {#if settlement && isValidPlatform}
    <div class="flex flex-col p-4 w-full overflow-y-auto">
      <SettlementCard
        id={settlementId}
        {settlement}
        checked={true}
        hasCheckbox={false}
      />
      <div
        class="flex flex-row items-end gap-4 py-4 border-b border-gray-200
             dark:border-neutral-600"
      >
        <div class="flex-1">
          <DropDownSelector
            id="platform-select-dropdown"
            clearable={false}
            label="Selected Platform"
            labelText={selectedPlatform.label}
            value={selectedPlatform.value}
            items={platforms}
            on:change={handlePlatformChange}
          />
        </div>
        <Button
          on:click={handleAddAdjustment}
          color="green"
          className="rounded-md  h-[40px] gap-1 w-44"
        >
          Add Adjustment
        </Button>
      </div>
      <SettlementDetailsTable
        selectedPlatform={selectedPlatform.value}
        {settlement}
      />
      <SettlementAdjustmentsTable
        headers={adjustmentsHeaders}
        rows={adjustmentsTableRows}
      />
    </div>
    <SettlementDetailsFooter
      {settlement}
      selectedPlatform={selectedPlatform.value}
      on:delete={openDeleteModal}
    />
    <AdjustmentsModal
      bind:showModal={showAdjustmentsModal}
      on:confirm={addAdjustment}
    />
    <DeleteSettlementsModal
      on:confirm={handleDelete}
      bind:showModal={showDeleteModal}
      selectedSettlementIds={new Set([settlementId])}
      {failedToDeleteSettlements}
      {isDeleting}
      details={{
        customerName: settlement.customerName,
        fromDate: settlement.fromDate,
        toDate: settlement.toDate,
      }}
    />
  {:else if isLoading}
    <div class="flex-1 flex h-full w-full items-center justify-center">
      <LoadingSpinner extraClass="w-12 h-12" />
    </div>
  {/if}
</div>
