<script lang="ts">
  import { onMount } from "svelte";

  import {
    fetchData,
    getSettlementPreference,
    saveSettlementPreference,
  } from "$lib/components/settlement/functions/get-settlement-data";
  import { getSettlementFilterOptions } from "$lib/components/settlement/functions/get-settlement-filter-options";
  import { getSortKeyAndSortOrder } from "$lib/components/settlement/functions/get-sort-key-and-sort-order";
  import List from "$lib/components/settlement/List.svelte";
  import DeleteSettlementsModal from "$lib/components/settlement/modals/DeleteSettlementsModal.svelte";
  import GenerateSettlementsModal from "$lib/components/settlement/modals/GenerateSettlementsModal.svelte";
  import SettlementFilter from "$lib/components/settlement/SettlementFilter.svelte";
  import {
    failureToast,
    infoToast,
  } from "$lib/components/ui/notifications/toast";
  import PaginationFooter from "$lib/components/ui/pagination/PaginationFooter.svelte";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import { request } from "$lib/services/api-caller/request";
  import { formatDateStringToYYYYMMDD } from "$lib/utils/date-utils";

  import type {
    SettlementFilters,
    SettlementSummary,
  } from "$lib/types/settlement/types";
  import type { RequestError } from "$lib/utils/errors/request-error";

  let showGenerateSettlementsModal = false;
  let showDeleteSettlementsModal = false;

  // Resetting failedToDeleteSettlements when the modal is closed
  $: if (!showDeleteSettlementsModal) {
    failedToDeleteSettlements = [];
  }

  let selectedRows = new Set<string>();

  const sortByValues: string[] = [
    "From Date: Old - New",
    "From Date: New - Old",
    "To Date: Old - New",
    "To Date: New - Old",
    "Client Name: A-Z",
    "Client Name: Z-A",
  ];

  let sortValue = sortByValues[0];

  let { sortKey, sortOrder } = getSortKeyAndSortOrder(sortValue);

  let isLoading = false;
  let isDeleting = false;
  let currentPage = 1;
  let recordsPerPage = 20;
  let totalCount = 0;
  let lastPage = 1;

  let loadedSettlementSummary: SettlementSummary[] = [];

  let settlementStateOptions: string[] = [];
  let clientTypeOptions: string[] = [];
  let frequencyTypeOptions: string[] = [];

  let filters: SettlementFilters = {
    status: "No Filter",
    textInputValue: "",
    clientType: "",
    displayAdjusted: "Show All",
    state: "",
    frequency: "",
    startDate: undefined,
    endDate: undefined,
  };

  let failedToDeleteSettlements: Array<{
    settlementId: string;
    customerName: string;
    serviceNumber: string;
    fromDate: string;
    toDate: string;
  }> = [];

  onMount(async () => {
    await loadFilters();
    await loadSettlementPreference();
    await loadData({
      pageNumber: currentPage,
      recordsPerPage,
      sortKey,
      sortOrder,
      filters,
    });
  });

  const loadSettlementPreference = async () => {
    try {
      const settlementPreference = await getSettlementPreference();
      const preferenceFilters = settlementPreference.filters;

      preferenceFilters.startDate &&= new Date(preferenceFilters.startDate);
      preferenceFilters.endDate &&= new Date(preferenceFilters.endDate);

      filters = { ...preferenceFilters };
      currentPage = settlementPreference.pageNumber;
      recordsPerPage = settlementPreference.recordsPerPage;
    } catch {
      failureToast("Failed to load settlement preference");
    }
  };

  const savePreference = async () => {
    try {
      await saveSettlementPreference({
        filters,
        pageNumber: currentPage,
        recordsPerPage,
        sortKey,
        sortOrder,
      });
    } catch {
      failureToast("Failed to save settlement preference");
    }
  };

  const handleFilterChange = async (
    event: CustomEvent<{
      filters: {
        clientType: string;
        displayAdjusted: string;
        endDate: Date;
        frequency: string;
        startDate: Date;
        state: string;
        status: string;
        textInputValue: string;
      };
    }>
  ) => {
    let filtersAreDifferent = false;

    for (const key in filters) {
      if (key === "startDate" || key === "endDate") {
        // Skip date comparison for now, as we are not handling date objects in this iteration
        continue;
      } else if (
        event.detail.filters[key as keyof typeof filters] !==
        filters[key as keyof typeof filters]
      ) {
        filtersAreDifferent = true;
        break;
      }
    }

    // We only want to load data and trigger a filters change if the startDate
    // and endDate are both set
    if (
      !filtersAreDifferent &&
      event.detail.filters.startDate !== filters.startDate &&
      event.detail.filters.endDate !== filters.endDate &&
      event.detail.filters.startDate &&
      event.detail.filters.endDate
    ) {
      filtersAreDifferent = true;
    }

    if (filtersAreDifferent) {
      filters = { ...event.detail.filters };
      currentPage = 1;
      await loadData({
        pageNumber: currentPage,
        recordsPerPage,
        sortKey,
        sortOrder,
        filters,
      });
      await savePreference();
    }
  };

  // Calling it load filters as we will be saving the filter preferences
  // to the DB later
  const loadFilters = async () => {
    const dropdownOptionsForFilters = await getSettlementFilterOptions();

    settlementStateOptions = dropdownOptionsForFilters.settlementStates;
    clientTypeOptions = dropdownOptionsForFilters.clientTypes;
    frequencyTypeOptions = dropdownOptionsForFilters.settlementFrequencies;
  };

  const loadData = async ({
    pageNumber,
    recordsPerPage,
    sortKey,
    sortOrder,
    filters,
  }: {
    pageNumber: number;
    recordsPerPage: number;
    sortKey: string;
    sortOrder: string;
    filters: SettlementFilters;
  }) => {
    isLoading = true;

    try {
      const { totalCount: total, settlements } = await fetchData({
        pageNumber,
        recordsPerPage,
        sortKey,
        sortOrder,
        filters,
      });
      loadedSettlementSummary = settlements.map((settlement) => ({
        ...settlement.summary,
      }));
      totalCount = total;

      lastPage = Math.ceil(totalCount / recordsPerPage);
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to fetch settlements");
    } finally {
      isLoading = false;
    }
  };

  const handlePageChange = async (
    event: CustomEvent<{ pageNumber: number }>
  ) => {
    selectedRows = new Set<string>();
    currentPage = event.detail.pageNumber;
    await loadData({
      pageNumber: currentPage,
      recordsPerPage,
      sortKey,
      sortOrder,
      filters,
    });
    await savePreference();
  };

  const handleRecordsPerPageChange = async (
    event: CustomEvent<{ records: number }>
  ) => {
    selectedRows = new Set<string>();
    recordsPerPage = event.detail.records;
    lastPage = Math.ceil(totalCount / recordsPerPage);
    currentPage = 1;
    await loadData({
      pageNumber: currentPage,
      recordsPerPage,
      sortKey,
      sortOrder,
      filters,
    });
    await savePreference();
  };

  const handleSortChange = async (event: CustomEvent<{ value: string }>) => {
    selectedRows = new Set<string>();
    currentPage = 1;
    ({ sortKey, sortOrder } = getSortKeyAndSortOrder(event.detail.value));
    await loadData({
      pageNumber: currentPage,
      recordsPerPage,
      sortKey,
      sortOrder,
      filters,
    });
    await savePreference();
  };

  const openGenerateSettlementsModal = () => {
    showGenerateSettlementsModal = true;
  };

  const openDeleteSettlementsModal = () => {
    showDeleteSettlementsModal = true;
  };

  const displayInProgressToast = () => {
    showGenerateSettlementsModal = false;
    infoToast(
      "Processing Settlements, please refresh the page in a bit to see results."
    );
  };

  const handleSettlementGeneration = async (
    event: CustomEvent<{
      frequency: string;
      frequencyId: string;
      fromDate: string;
      toDate: string;
    }>
  ) => {
    const { frequency, frequencyId, fromDate, toDate } = event.detail;

    displayInProgressToast();

    try {
      const response = await request.post<{
        success: boolean;
        message?: string;
        data?: {
          settlement: {
            jobId: number;
            frequencyName: string;
            fromDate: string;
            toDate: string;
          };
        };
      }>("/settlement/generate", {
        frequencyId: Number(frequencyId),
        fromDate,
        toDate,
      });

      if (response.success) {
        const message = `Settlement generated successfully for frequency "${frequency}" from ${formatDateStringToYYYYMMDD(fromDate)} to ${formatDateStringToYYYYMMDD(toDate)}.`;
        infoToast(message);
      }
    } catch (error) {
      const requestError = error as RequestError;
      const errorMessage =
        requestError.message ?? "An unexpected error occurred.";
      failureToast(errorMessage);
    }
  };

  const handleDeleteSettlements = async () => {
    try {
      isDeleting = true;

      const response = await request.delete<{
        success: boolean;
        failedSettlementIds?: number[];
      }>("/settlement", {
        settlementIds: [...selectedRows].filter(
          (id) =>
            !failedToDeleteSettlements.some(
              (settlement) => settlement.settlementId === id
            )
        ),
      });

      if (response.success) {
        infoToast("Settlements deleted successfully.");

        showDeleteSettlementsModal = false;
        selectedRows = new Set<string>();

        void loadData({
          pageNumber: currentPage,
          recordsPerPage,
          sortKey,
          sortOrder,
          filters,
        });
      } else {
        if (!response.failedSettlementIds) {
          failureToast("Internal server error. Failed to delete settlements.");
          showDeleteSettlementsModal = false;

          return;
        }

        failedToDeleteSettlements = response.failedSettlementIds.map((id) => {
          const settlement = loadedSettlementSummary.find(
            (settlement) => settlement.id === String(id)
          );

          return {
            settlementId: String(id),
            customerName: settlement?.customerName ?? "Unknown",
            serviceNumber: settlement?.serviceNumber ?? "Unknown",
            fromDate: settlement?.fromDate ?? "Unknown",
            toDate: settlement?.toDate ?? "Unknown",
          };
        });
      }
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(`Failed to delete settlements: ${requestError.message}`);

      showDeleteSettlementsModal = false;
    } finally {
      isDeleting = false;
    }
  };
</script>

<div
  class="flex flex-col max-lg:w-full lg:w-[calc(100%-195px)] h-screen dark:bg-black"
>
  <SettlementFilter
    bind:clientTypes={clientTypeOptions}
    bind:settlementStates={settlementStateOptions}
    bind:frequencyTypes={frequencyTypeOptions}
    on:generateSettlementsClicked={openGenerateSettlementsModal}
    on:filtersChange={handleFilterChange}
  />
  <div class="flex flex-col h-full overflow-y-auto w-full dark:bg-black">
    {#if isLoading}
      <div class="flex-1 flex h-full w-full items-center justify-center">
        <LoadingSpinner extraClass="w-12 h-12" />
      </div>
    {:else}
      <List
        {loadedSettlementSummary}
        bind:selectedRows
        on:delete={openDeleteSettlementsModal}
      />
    {/if}
  </div>

  <PaginationFooter
    id="settlement-footer"
    {lastPage}
    {currentPage}
    {sortByValues}
    bind:sortValue
    totalNumberOfItemsListed={totalCount}
    limit={String(recordsPerPage)}
    on:pageChange={handlePageChange}
    on:recordsPerPageChange={handleRecordsPerPageChange}
    on:sortChange={handleSortChange}
  />
</div>

<GenerateSettlementsModal
  on:confirm={handleSettlementGeneration}
  bind:showModal={showGenerateSettlementsModal}
/>

<DeleteSettlementsModal
  on:confirm={handleDeleteSettlements}
  bind:showModal={showDeleteSettlementsModal}
  selectedSettlementIds={selectedRows}
  {failedToDeleteSettlements}
  {isDeleting}
/>
