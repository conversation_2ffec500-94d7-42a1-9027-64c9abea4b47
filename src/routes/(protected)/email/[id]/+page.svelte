<script lang="ts">
  import { onMount } from "svelte";

  import { page } from "$app/stores";
  import EmailDetails from "$lib/components/email/details/EmailDetails.svelte";
  import EmailDetailsFooter from "$lib/components/email/details/EmailDetailsFooter.svelte";
  import EmailTemplate from "$lib/components/email/details/EmailTemplate.svelte";
  import {
    fetchEmailById,
    getEmailTemplate,
  } from "$lib/components/email/functions/get-email-data";
  import TextArea from "$lib/components/ui/inputs/TextAreaInput.svelte";
  import Modal from "$lib/components/ui/modals/Modal.svelte";
  import {
    failureToast,
    successToast,
  } from "$lib/components/ui/notifications/toast";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import { request } from "$lib/services/api-caller/request";

  import type { EmailResponse } from "$lib/types/email-table/types";
  import type { Page } from "@sveltejs/kit";

  let emailResponse: EmailResponse;
  let emailId: string | undefined;
  let loading = false;
  let isCommentModalOpen = false;
  let commentValue = "";
  let loadingTemplate = false;
  let endBalanceIsPresent = false;

  async function fetchEmailData(id: string) {
    loading = true;

    try {
      emailResponse = (await fetchEmailById(id)) as EmailResponse;
      commentValue = emailResponse.email.comment;
      endBalanceIsPresent = emailResponse.email.isEndBalance;
    } catch {
      failureToast("Failed to fetch email details");
    } finally {
      loading = false;
    }
  }

  async function handleAddComment() {
    try {
      loading = true;
      isCommentModalOpen = false;

      await request.put("/email", {
        emailIds: [emailId],
        comment: commentValue,
      });
      await fetchEmailData(emailId!);
      successToast("Comment added successfully");
    } catch {
      loading = false;
      failureToast("Failed to add a comment");
    }
  }

  async function updateIsEndBalance(id: string | number, checked: boolean) {
    try {
      loadingTemplate = true;
      await request.put("/email", { emailIds: [id], isEndBalance: !checked });
      const html = await getEmailTemplate(emailId!);

      emailResponse.email.isEndBalance = !checked;
      emailResponse.html = html;
    } catch {
      failureToast("Failed to change value of End Balance toggle");
    } finally {
      loadingTemplate = false;
    }
  }

  onMount(async () => {
    const currentPage = $page as Page<{ id: string }>;
    emailId = currentPage?.params?.id;

    if (emailId) {
      await fetchEmailData(emailId);
    }
  });
</script>

<div class="flex flex-col w-full h-full">
  {#if loading}
    <div class="flex-1 flex h-full items-center justify-center">
      <LoadingSpinner extraClass="w-12 h-12" />
    </div>
  {:else if emailResponse?.email}
    <div class="grid grid-cols-1 lg:grid-cols-10 flex-1 overflow-y-auto">
      <div class="lg:col-span-6 pr-4">
        <EmailDetails
          emailData={emailResponse.email}
          {endBalanceIsPresent}
          {updateIsEndBalance}
        />
      </div>
      <div class="lg:col-span-4 pl-4 mt-4 sm:mt-0">
        <EmailTemplate html={emailResponse.html} {loadingTemplate} />
      </div>
    </div>
  {/if}
  <EmailDetailsFooter
    bind:isCommentModalOpen
    isEmailSent={Boolean(emailResponse?.email?.sentById)}
  />
  <Modal
    title="Add Comment"
    subtitle="Comment appears in the email body"
    id="add-comment-modal"
    closeOnOutsideClick={false}
    bind:showModal={isCommentModalOpen}
    on:confirm={handleAddComment}
  >
    <div slot="content">
      <TextArea
        id="email-id-text-area"
        label="Comment"
        placeholder="Type Here"
        bind:value={commentValue}
        disabled={false}
      />
    </div>
  </Modal>
</div>
