<script lang="ts">
  import { onMount } from "svelte";

  import {
    fetchData,
    getFilterOptions,
    getEmailPreference,
    type FilterOptionsResponse,
  } from "$lib/components/email/functions/get-email-data";
  import EmailSummaryFilters from "$lib/components/email/summary/EmailSummaryFilters.svelte";
  import EmailTable from "$lib/components/email/summary/EmailSummaryTable.svelte";
  import TextArea from "$lib/components/ui/inputs/TextAreaInput.svelte";
  import Modal from "$lib/components/ui/modals/Modal.svelte";
  import {
    failureToast,
    successToast,
    infoToast,
  } from "$lib/components/ui/notifications/toast";
  import PaginationFooter from "$lib/components/ui/pagination/PaginationFooter.svelte";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import { emailStatuses } from "$lib/constants/email";
  import { request } from "$lib/services/api-caller/request";

  import type { EmailData, EmailFilters } from "$lib/types/email-table/types";

  const baseEmailFilters = {
    status: emailStatuses[0],
    subject: "",
    toEmail: "",
    startSendDate: undefined,
    endSendDate: undefined,
    entity: "",
    frequency: "",
    merchantType: "",
    sortColumnName: "",
    sortDirection: "none",
  } satisfies EmailFilters;

  let isLoading = false;
  let filterOptions: FilterOptionsResponse = {
    entities: {},
    frequencies: {},
    merchantTypes: {},
  };
  let currentPage = 1;
  let recordsPerPage = 20;
  let totalCount = 0;
  let emailData: EmailData[] = [];
  let selectedEmails = new Set<string>();
  let isCommentModalOpen = false;
  let filters: EmailFilters = { ...baseEmailFilters };
  let lastPage = 1;
  let comment = "";

  async function loadData(pageNumber: number, recordsPerPage: number) {
    isLoading = true;
    const dataFilters = { ...filters };

    if (Number.isNaN(Number(dataFilters.entity))) {
      dataFilters.entity = String(filterOptions.entities[dataFilters.entity]);
    }

    if (Number.isNaN(Number(dataFilters.frequency))) {
      dataFilters.frequency = String(
        filterOptions.frequencies[dataFilters.frequency]
      );
    }

    if (Number.isNaN(Number(dataFilters.merchantType))) {
      dataFilters.merchantType = String(
        filterOptions.merchantTypes[dataFilters.merchantType]
      );
    }

    try {
      const { totalCount: total, data } = await fetchData({
        pageNumber,
        recordsPerPage,
        ...dataFilters,
      });
      totalCount = total;
      emailData = data;
      selectedEmails = new Set<string>();
      lastPage = Math.ceil(totalCount / recordsPerPage);
    } catch {
      failureToast("Unable to fetch emails and save filters");
    } finally {
      isLoading = false;
    }
  }

  async function loadFilterOptions() {
    try {
      filterOptions = await getFilterOptions();
    } catch {
      failureToast("Failed to load filter options");
    }
  }

  async function loadEmailPreference() {
    try {
      const emailPreference = await getEmailPreference();
      const preferenceFilters = emailPreference.filters;

      preferenceFilters.frequency = getFilterOptionNameFromId(
        preferenceFilters.frequency,
        filterOptions.frequencies
      );
      preferenceFilters.entity = getFilterOptionNameFromId(
        preferenceFilters.entity,
        filterOptions.entities
      );
      preferenceFilters.merchantType = getFilterOptionNameFromId(
        preferenceFilters.merchantType,
        filterOptions.merchantTypes
      );

      filters = { ...preferenceFilters };
      currentPage = emailPreference.pageNumber;
      recordsPerPage = emailPreference.recordsPerPage;
    } catch {
      failureToast("Failed to load email preference");
    }
  }

  function getFilterOptionNameFromId(
    filterOptionId: string,
    filterOptions: Record<string, number>
  ) {
    if (filterOptionId === "") {
      return "";
    }

    const filterOptionName = Object.keys(filterOptions).find(
      (key) => filterOptions[key] === Number(filterOptionId)
    );

    return filterOptionName ?? "";
  }

  onMount(async () => {
    await loadFilterOptions();
    await loadEmailPreference();
    await loadData(currentPage, recordsPerPage);
  });

  function handlePageChange(event: CustomEvent<{ pageNumber: number }>) {
    currentPage = event.detail.pageNumber;
    void loadData(currentPage, recordsPerPage);
  }

  function handleRecordsPerPageChange(event: CustomEvent<{ records: number }>) {
    recordsPerPage = event.detail.records;
    lastPage = Math.ceil(totalCount / recordsPerPage);
    currentPage = 1;
    void loadData(currentPage, recordsPerPage);
  }

  function handleFiltersChange(event: CustomEvent<{ filters: EmailFilters }>) {
    filters = event.detail.filters;
    currentPage = 1;
    void loadData(currentPage, recordsPerPage);
  }

  function handleSortChange(
    event: CustomEvent<{
      sortColumnName: string;
      sortDirection: "desc" | "asc" | "none";
    }>
  ) {
    filters.sortColumnName = event.detail.sortColumnName;
    filters.sortDirection = event.detail.sortDirection;
    void loadData(currentPage, recordsPerPage);
  }

  async function handleAddComment() {
    isLoading = true;

    try {
      isCommentModalOpen = false;
      await request.put("/email", { emailIds: [...selectedEmails], comment });
      await loadData(currentPage, recordsPerPage);
      successToast("Comment added successfully");
    } catch {
      isLoading = false;
      failureToast("Failed to add a comment");
    }
  }

  function handleSelectionChange(
    event: CustomEvent<{ selectedRowIds: string[] }>
  ) {
    selectedEmails = new Set(event.detail.selectedRowIds);
  }

  function handleOverlayAction(event: CustomEvent<{ actionId: string }>) {
    const { actionId } = event.detail;

    if (selectedEmails.size > 0) {
      switch (actionId) {
        case "addComment": {
          isCommentModalOpen = true;
          break;
        }

        default: {
          throw new Error(`Unhandled actionId: ${actionId}`);
        }
      }
    } else {
      infoToast("Please select at least one email");
    }
  }
</script>

<div class="flex flex-col max-lg:w-full lg:w-[calc(100%-180px)] h-full">
  <EmailSummaryFilters
    entities={Object.keys(filterOptions.entities)}
    frequencies={Object.keys(filterOptions.frequencies)}
    merchantTypes={Object.keys(filterOptions.merchantTypes)}
    {baseEmailFilters}
    {emailStatuses}
    {filters}
    on:filtersChange={handleFiltersChange}
  />

  <div class="flex-1 overflow-y-auto scrollbar-hide">
    <div class={emailData?.length ? "visible" : "invisible"}>
      <EmailTable
        {emailData}
        emailStatus={filters?.status}
        {isLoading}
        isSortCleared={!filters.sortColumnName}
        sortState={{
          id: filters.sortColumnName,
          direction: filters.sortDirection,
        }}
        on:sortChange={handleSortChange}
        on:selectionChange={handleSelectionChange}
        on:overlayAction={handleOverlayAction}
      />
    </div>
    {#if isLoading}
      <div class="flex-1 flex h-full w-full items-center justify-center">
        <LoadingSpinner extraClass="w-12 h-12" />
      </div>
    {/if}
  </div>

  <PaginationFooter
    id="email-footer"
    {lastPage}
    {currentPage}
    totalNumberOfItemsListed={totalCount}
    limit={String(recordsPerPage)}
    on:pageChange={handlePageChange}
    on:recordsPerPageChange={handleRecordsPerPageChange}
  />

  <!-- Modals -->
  <Modal
    title="Add Comment"
    subtitle="Comment appears in the email body"
    id="add-comments-modal"
    closeOnOutsideClick={false}
    bind:showModal={isCommentModalOpen}
    on:confirm={handleAddComment}
  >
    <div slot="content">
      <TextArea
        id="email-id-text-area"
        label="Comment"
        placeholder="Type Here"
        bind:value={comment}
        disabled={false}
      />
    </div>
  </Modal>
</div>
