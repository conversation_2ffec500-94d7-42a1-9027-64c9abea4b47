<script lang="ts">
  import CopyTextInput from "$lib/components/ui/inputs/CopyTextInput.svelte";
  import { authStore } from "$lib/stores/auth-store";
</script>

<main id="profile" class="grid w-full pb-16">
  <div class="relative flex h-screen flex-col">
    <div
      class="flex max-h-[calc(100%-4rem)] w-full flex-col gap-4 overflow-auto p-6"
    >
      <CopyTextInput
        id="user-profile-account-username-input"
        label={"Username"}
        value={$authStore.userProfile?.fullName ?? ""}
      />
      <CopyTextInput
        id="user-profile-account-email-input"
        label={"Email"}
        value={$authStore.userProfile?.email ?? ""}
      />
      <CopyTextInput
        id="user-profile-account-roles-input"
        label={"Roles"}
        value={$authStore.userProfile?.roles
          .map((role) => role.name)
          .join(", ") ?? ""}
      />
    </div>
  </div>
</main>
