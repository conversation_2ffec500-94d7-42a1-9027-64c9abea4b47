<script lang="ts">
  import { SvelteToast } from "@zerodevx/svelte-toast";
  import { onMount } from "svelte";

  import { goto } from "$app/navigation";
  import { page } from "$app/stores";
  import { authCheck } from "$lib/components/login/functions/auth-check";
  import { performLegacyLoginAndActions } from "$lib/components/login/functions/perform-legacy-actions";
  import Nav from "$lib/components/nav/Nav.svelte";
  import { authStore } from "$lib/stores/auth-store";

  import "../app.css";
  import "bootstrap-icons/font/bootstrap-icons.css";

  $: pageRoute = $page.route.id;
  $: ({ userProfile: user, isLoggedIn } = $authStore);
  $: isAuthenticated = Boolean(isLoggedIn && user);

  onMount(async () => {
    try {
      const { url } = $page;
      const token = url.searchParams.get("token");
      const pathName = url.pathname;
      const query = getQueryParameters(url.searchParams);

      if (token) {
        isAuthenticated = await performLegacyLoginAndActions(
          String(token),
          pathName,
          query
        );

        if (isAuthenticated) {
          const queryString = new URLSearchParams(query).toString();
          await goto(`${$page.url.pathname}?${queryString}`);

          return;
        }
      } else {
        const { isSuccess } = await authCheck();
        isAuthenticated = isSuccess;
      }

      if (!isAuthenticated) {
        await goto("/login");
      }
    } catch {
      await goto("/login");
    }
  });

  function getQueryParameters(
    searchParameters: URLSearchParams
  ): Record<string, string> {
    const parameters: Record<string, string> = {};

    for (const [key, value] of searchParameters.entries()) {
      if (key !== "token") {
        parameters[key] = value;
      }
    }

    return parameters;
  }
</script>

<div
  class={`h-screen w-screen ${isAuthenticated ? "flex overflow-hidden max-lg:flex-col max-lg:pt-16" : ""}`}
>
  {#if isAuthenticated}
    <Nav pageRoute={pageRoute ?? ""}></Nav>
  {/if}
  <slot />
</div>

<SvelteToast />
