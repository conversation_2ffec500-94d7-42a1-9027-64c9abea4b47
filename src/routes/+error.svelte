<script lang="ts">
  import { onMount, onDestroy } from "svelte";

  import { goto } from "$app/navigation";
  import error404 from "$lib/assets/errors/error-404.mp4";
  import Button from "$lib/components/ui/buttons/Button.svelte";

  let videoReference: HTMLVideoElement | undefined;
  let interval: number | ReturnType<typeof setInterval>;

  function playVideo() {
    if (videoReference) {
      videoReference.currentTime = 0;
      void videoReference.play();
    }
  }

  onMount(() => {
    // Set up the interval to play the video every 4 seconds
    interval = setInterval(() => {
      playVideo();
    }, 4000);
  });

  onDestroy(() => {
    clearInterval(interval);
  });

  async function goToHome() {
    await goto("/");
  }
</script>

<div
  class="w-full h-screen flex flex-col items-center justify-start py-4 px-36"
>
  <video
    bind:this={videoReference}
    class="w-full h-auto max-h-[70vh] object-cover"
    muted
    autoplay
  >
    <source src={error404} type="video/mp4" />
    Your browser does not support the video tag.
  </video>

  <div class="w-full flex flex-col items-center justify-center text-center">
    <p class="text-4xl font-bold mt-6">Page Not Found</p>

    <p class="text-lg mt-4 max-w-2xl">
      Oops! It seems you've wandered off. The page you are looking for doesn't
      exist. But don't worry, we can guide you back to the right place.
    </p>

    <div class="mt-6">
      <Button on:click={goToHome} label={"Go to Home"} width={"w-auto"} />
    </div>
  </div>
</div>
