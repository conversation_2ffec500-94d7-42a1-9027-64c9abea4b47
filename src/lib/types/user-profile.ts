import { type Permission } from "$lib/constants/access/permissions";
import { type Resource } from "$lib/constants/access/resources";

type User = {
  id: number;
  fullName: string;
  email: string;
};

type UserWithRoles = User & {
  roles: Role[];
};

type Role = {
  id: number;
  name: string;
  isEditable: boolean;
  rule: Rule;
};

type RoleWithUsers = Role & {
  users: User[];
};

type Rule = Record<Resource, Permission[]>;

export type { UserWithRoles, User, Role, Rule, RoleWithUsers };
