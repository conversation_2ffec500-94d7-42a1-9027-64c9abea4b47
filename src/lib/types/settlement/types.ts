type SettlementStatus =
  | "Processing"
  | "Skipped"
  | "Error"
  | "Approval Pending"
  | "Approval Processing"
  | "Approval Error"
  | "Approval Success"
  | "Status Unknown";

type SettlementSummary = {
  id: string;
  customerName: string;
  serviceNumber: string;
  customerType: string;
  status: SettlementStatus;
  netPayout: string;
  endBalance: string;
  fromDate: string;
  toDate: string;
  platformSettlements: PlatformSettlements;
  error?: string;
};

type GroupedSettlement = {
  customerId: number;
  fromDate: Date;
  summary: SettlementSummary;
};

type PlatformSettlements = Partial<Record<PlatformCode, SettlementDetails>>;

const platforms = [
  "IDP",
  "ETI",
  "ETF",
  "RFM",
  "ETO",
  "RTO",
  "RTX",
  "ACH",
  "ANR",
  "ANX",
  "KYC",
] as const;
type Platform = (typeof platforms)[number];

type SettlementFilters = {
  status: string;
  textInputValue: string;
  clientType: string;
  displayAdjusted: string;
  state: string;
  frequency: string;
  startDate?: Date;
  endDate?: Date;
};

const platformCodes = [
  "IDP",
  "ETI",
  "ETF",
  "RFM",
  "ETO",
  "RTO",
  "RTX",
  "ACH",
  "ANR",
  "ANX",
  "KYC",
  "SUMMARY",
] as const;
type PlatformCode = (typeof platformCodes)[number];

type SettlementDetails = {
  labelName: string;
  isNonZero: boolean;
  transactionCount: number;
  totalTransactionAmount: number;
  refundCount: number;
  totalRefundAmount: number;
  gatewayFee: number;
  transactionFee: number;
  salesFee: number;
  refundFee: number;
  totalFailedAmount: number;
  endBalance: number;
  total2FaRejectAmount: number;
  total2FaRejectCount: number;
  txnAmountRTO_R: number;
  txnCountETI_R1: number;
  minimumFeeTotal: number;
  minimumFeeCount: number;
  totalMinimumAmount: number;
  partialReturnAmountRTO: number;
  partialReturnCountRTO: number;
  totalPayable: number;
  isAdjusted: boolean;
  totalPayout?: number;
  totalAdjustments?: number;
  netPayout?: number;
  totalCosts?: number;
  adjustments?: AdjustmentDetails[];
  kycDetails?: KycDetails;
};

type AdjustmentDetails = {
  label: string;
  amount: number;
  displayCommentExcel: boolean;
  comment: string;
};

type KycDetails = {
  KY1?: { transactionCount: number; totalTransactionAmount: number };
  KY2?: { transactionCount: number; totalTransactionAmount: number };
  KY3?: { transactionCount: number; totalTransactionAmount: number };
  KY4?: { transactionCount: number; totalTransactionAmount: number };
  KY5?: { transactionCount: number; totalTransactionAmount: number };
  KY6?: { transactionCount: number; totalTransactionAmount: number };
};

type SettlementHistory = {
  jobId: number;
  fromDate: string;
  toDate: string;
  status: string;
  userId: number;
  userName: string;
  generatedAt: string;
  meta?: {
    totalCount: number;
    successCount: number;
    skippedCount: number;
    errorCount: number;
    timeTakenInSec: number;
  };
};

type SettlementPreference = {
  filters: SettlementFilters;
  pageNumber: number;
  recordsPerPage: number;
  sortKey: string;
  sortOrder: string;
};

export {
  type SettlementStatus,
  type SettlementSummary,
  type PlatformSettlements,
  type Platform,
  platforms,
  type GroupedSettlement,
  type SettlementFilters,
  platformCodes,
  type PlatformCode,
  type SettlementDetails,
  type SettlementHistory,
  type SettlementPreference,
};
