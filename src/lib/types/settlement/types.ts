type SettlementStatus =
  | "Processing"
  | "Skipped"
  | "Error"
  | "Approval Pending"
  | "Approval Processing"
  | "Approval Error"
  | "Approval Success"
  | "Status Unknown";

type SettlementSummary = {
  id: string;
  customerName: string;
  serviceNumber: string;
  customerType: string;
  status: SettlementStatus;
  netPayout: string;
  endBalance: string;
  fromDate: string;
  toDate: string;
  platformSettlements: PlatformSettlements;
  error?: string;
};

type GroupedSettlement = {
  customerId: number;
  fromDate: Date;
  summary: SettlementSummary;
};

type PlatformSettlements = Partial<Record<Platform, { isAdjusted: boolean }>>;

const platforms = [
  "IDP",
  "ETI",
  "ETF",
  "RFM",
  "ETO",
  "RTO",
  "RTX",
  "ACH",
  "ANR",
  "ANX",
  "KYC",
] as const;
type Platform = (typeof platforms)[number];

type SettlementFilters = {
  status: string;
  textInputValue: string;
  clientType: string;
  displayAdjusted: string;
  state: string;
  frequency: string;
  startDate?: Date;
  endDate?: Date;
};

export {
  type SettlementStatus,
  type SettlementSummary,
  type PlatformSettlements,
  type Platform,
  platforms,
  type GroupedSettlement,
  type SettlementFilters,
};
