import type { emailStatuses } from "$lib/constants/email";

export type EmailData = {
  id: string;
  clientName: string;
  serviceNumber: string;
  to: string[];
  cc: string[];
  subject: string;
  wireAmount: string;
  endBalance: string;
  isEndBalance: boolean;
  sendDate: string;
  sentById?: number;
  sentAt?: string;
};

export type EmailById = EmailData & {
  attachments: {
    rootFolderPath: string;
    files: Array<{ filePath: string; isFileExist: boolean }>;
  };
  comment: string;
  entityName: string;
};

export type EmailResponse = {
  email: EmailById;
  html: string;
};

export type EmailStatus = (typeof emailStatuses)[number];

export type EmailFilters = {
  status: string;
  subject: string;
  toEmail: string;
  startSendDate?: Date;
  endSendDate?: Date;
  entity: string;
  frequency: string;
  merchantType: string;
  sortDirection: "desc" | "asc" | "none";
  sortColumnName: string;
};

export type EmailPreference = {
  filters: EmailFilters;
  pageNumber: number;
  recordsPerPage: number;
};
