<svg width="863" height="721" viewBox="0 0 863 721" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_886_5381)">
<circle cx="514.348" cy="348.76" r="308" fill="#FAB765"/>
</g>
<g filter="url(#filter1_f_886_5381)">
<circle cx="358.348" cy="362.76" r="294" fill="#E44A3E" fill-opacity="0.5"/>
</g>
<g filter="url(#filter2_f_886_5381)">
<circle cx="237.455" cy="35.8673" r="17.8857" transform="rotate(0.714446 237.455 35.8673)" fill="url(#paint0_linear_886_5381)"/>
</g>
<g filter="url(#filter3_f_886_5381)">
<circle cx="822.455" cy="610.867" r="17.8857" transform="rotate(0.714446 822.455 610.867)" fill="url(#paint1_linear_886_5381)"/>
</g>
<defs>
<filter id="filter0_f_886_5381" x="166.348" y="0.76001" width="696" height="696" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20" result="effect1_foregroundBlur_886_5381"/>
</filter>
<filter id="filter1_f_886_5381" x="0.3479" y="4.76001" width="716" height="716" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_886_5381"/>
</filter>
<filter id="filter2_f_886_5381" x="211.57" y="9.98157" width="51.7715" height="51.7715" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_886_5381"/>
</filter>
<filter id="filter3_f_886_5381" x="796.569" y="584.982" width="51.7715" height="51.7715" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_886_5381"/>
</filter>
<linearGradient id="paint0_linear_886_5381" x1="248.845" y1="33.8641" x2="221.487" y2="58.6466" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAB765"/>
<stop offset="1" stop-color="#E45C3E"/>
</linearGradient>
<linearGradient id="paint1_linear_886_5381" x1="833.845" y1="608.864" x2="806.487" y2="633.647" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAB765"/>
<stop offset="1" stop-color="#E45C3E"/>
</linearGradient>
</defs>
</svg>
