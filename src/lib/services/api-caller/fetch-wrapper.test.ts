import { describe, it, expect, vi, beforeEach } from "vitest";

import { fetchWrapper } from "./fetch-wrapper";

import { authStore } from "$lib/stores/auth-store";
import { RequestError } from "$lib/utils/errors/request-error";

vi.mock("$lib/stores/auth-store", () => ({
  authStore: {
    set: vi.fn(),
  },
}));

describe("fetchWrapper", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("should return JSON data when responseType is json", async () => {
    const mockResponseData = { message: "Success" };
    globalThis.fetch = vi.fn().mockResolvedValue({
      ok: true,
      status: 200,
      url: "/test-url",
      json: vi.fn().mockResolvedValue(mockResponseData),
    });

    const result = await fetchWrapper("GET", "/test-url");
    expect(result).toEqual(mockResponseData);
  });

  it("should return text data when responseType is text", async () => {
    const mockResponseData = "Success";
    globalThis.fetch = vi.fn().mockResolvedValue({
      ok: true,
      status: 200,
      url: "/test-url",
      text: vi.fn().mockResolvedValue(mockResponseData),
    });

    const result = await fetchWrapper("GET", "/test-url", undefined, "text");
    expect(result).toEqual(mockResponseData);
  });

  it("should return blob data when responseType is blob", async () => {
    const mockBlob = new Blob(["blob content"], { type: "text/plain" });
    globalThis.fetch = vi.fn().mockResolvedValue({
      ok: true,
      status: 200,
      url: "/test-url",
      blob: vi.fn().mockResolvedValue(mockBlob),
    });

    const result = await fetchWrapper("GET", "/test-url", undefined, "blob");
    expect(result).toEqual(mockBlob);
  });

  it("should throw RequestError when response is not ok", async () => {
    const mockErrorData = { error: "Not Found" };
    globalThis.fetch = vi.fn().mockResolvedValue({
      ok: false,
      status: 404,
      statusText: "Not Found",
      url: "/test-url",
      json: vi.fn().mockResolvedValue(mockErrorData),
    });

    await expect(fetchWrapper("GET", "/test-url")).rejects.toThrow(
      RequestError
    );
  });

  it("should set authStore.isLoggedIn to false when response status is 401 and url does not include /session", async () => {
    const mockErrorData = { error: "Unauthorized" };
    globalThis.fetch = vi.fn().mockResolvedValue({
      ok: false,
      status: 401,
      statusText: "Unauthorized",
      url: "/test-url",
      json: vi.fn().mockResolvedValue(mockErrorData),
    });

    await expect(fetchWrapper("GET", "/test-url")).rejects.toThrow(
      RequestError
    );

    expect(authStore.set).toHaveBeenCalledWith({ isLoggedIn: false });
  });

  it("should not change authStore when response status is 401 and url includes /session", async () => {
    const mockErrorData = { error: "Unauthorized" };
    globalThis.fetch = vi.fn().mockResolvedValue({
      ok: false,
      status: 401,
      statusText: "Unauthorized",
      url: "/session",
      json: vi.fn().mockResolvedValue(mockErrorData),
    });

    await expect(fetchWrapper("GET", "/session")).rejects.toThrow(RequestError);

    expect(authStore.set).not.toHaveBeenCalled();
  });

  it("should throw RequestError when response cannot be parsed as JSON", async () => {
    globalThis.fetch = vi.fn().mockResolvedValue({
      ok: true,
      status: 200,
      url: "/test-url",
      json: vi.fn().mockRejectedValue(new Error("Invalid JSON")),
    });

    await expect(fetchWrapper("GET", "/test-url")).rejects.toThrow(
      RequestError
    );
  });

  it("should throw RequestError when fetch throws an error", async () => {
    const fetchError = new Error("Network Error");
    globalThis.fetch = vi.fn().mockRejectedValue(fetchError);

    await expect(fetchWrapper("GET", "/test-url")).rejects.toThrow(
      RequestError
    );
  });

  it("should send the correct request with body", async () => {
    const mockResponseData = { message: "Success" };
    const requestBody = { key: "value" };

    globalThis.fetch = vi.fn().mockResolvedValue({
      ok: true,
      status: 200,
      url: "/test-url",
      json: vi.fn().mockResolvedValue(mockResponseData),
    });

    await fetchWrapper("POST", "/test-url", requestBody);

    expect(globalThis.fetch).toHaveBeenCalledWith(
      "/test-url",
      expect.objectContaining({
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      })
    );
  });
});
