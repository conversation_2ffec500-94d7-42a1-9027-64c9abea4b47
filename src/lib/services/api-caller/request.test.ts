import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  type MockedFunction,
} from "vitest";

import { fetchWrapper } from "./fetch-wrapper";
import { request } from "./request";

vi.mock("$env/static/public", () => ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  PUBLIC_API_URL: "https://api.test.com",
}));

vi.mock("./fetch-wrapper", () => ({
  fetchWrapper: vi.fn(),
}));

describe("request module", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("should call fetchWrapper once with correct parameters when using request.get", async () => {
    const mockResponse = { data: "test data" };
    (fetchWrapper as MockedFunction<typeof fetchWrapper>).mockResolvedValue(
      mockResponse
    );

    const result = await request.get("/endpoint");

    expect(fetchWrapper).toHaveBeenCalledTimes(1);
    expect(fetchWrapper).toHaveBeenCalledWith(
      "GET",
      "https://api.test.com/endpoint",
      undefined,
      "json",
      undefined
    );
    expect(result).toEqual(mockResponse);
  });

  it("should call fetchWrapper once with correct parameters when using request.post", async () => {
    const mockResponse = { success: true };
    const requestBody = { key: "value" };
    (fetchWrapper as MockedFunction<typeof fetchWrapper>).mockResolvedValue(
      mockResponse
    );

    const result = await request.post("/endpoint", requestBody);

    expect(fetchWrapper).toHaveBeenCalledTimes(1);
    expect(fetchWrapper).toHaveBeenCalledWith(
      "POST",
      "https://api.test.com/endpoint",
      requestBody,
      "json",
      undefined
    );
    expect(result).toEqual(mockResponse);
  });

  it("should call fetchWrapper once with correct parameters when using request.put", async () => {
    const mockResponse = { updated: true };
    const requestBody = { id: 1, value: "new" };
    (fetchWrapper as MockedFunction<typeof fetchWrapper>).mockResolvedValue(
      mockResponse
    );

    const result = await request.put("/endpoint", requestBody);

    expect(fetchWrapper).toHaveBeenCalledTimes(1);
    expect(fetchWrapper).toHaveBeenCalledWith(
      "PUT",
      "https://api.test.com/endpoint",
      requestBody,
      "json",
      undefined
    );
    expect(result).toEqual(mockResponse);
  });

  it("should call fetchWrapper once with correct parameters when using request.delete", async () => {
    const mockResponse = { deleted: true };
    (fetchWrapper as MockedFunction<typeof fetchWrapper>).mockResolvedValue(
      mockResponse
    );

    const result = await request.delete("/endpoint");

    expect(fetchWrapper).toHaveBeenCalledTimes(1);
    expect(fetchWrapper).toHaveBeenCalledWith(
      "DELETE",
      "https://api.test.com/endpoint",
      undefined,
      "json",
      undefined
    );
    expect(result).toEqual(mockResponse);
  });
});
