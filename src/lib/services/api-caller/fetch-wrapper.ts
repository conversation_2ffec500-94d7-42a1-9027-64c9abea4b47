import { authStore } from "$lib/stores/auth-store";
import { RequestError } from "$lib/utils/errors/request-error";

type RequestOptions = Omit<RequestInit, "body" | "method">;
type ResponseType = "json" | "blob" | "text";

// eslint-disable-next-line max-params
async function fetchWrapper<T>(
  method: string,
  url: string,
  body?: Record<string, unknown> | string,
  reponseType: ResponseType = "json",
  options?: RequestOptions
): Promise<T> {
  const requestOptions: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: body ? JSON.stringify(body) : undefined,
    ...options,
  };

  try {
    const response = await fetch(url, requestOptions);

    // If we get a 401 response and it is not coming from a login request
    if (response.status === 401 && !url.includes("/session")) {
      // Set the logged in state to false
      authStore.set({ isLoggedIn: false });
    }

    if (!response.ok) {
      await _handleFetchError(response);
    }

    if (reponseType === "blob") {
      return await _parseBlobResponse<T>(response);
    }

    if (reponseType === "text") {
      return await _parseTextResponse<T>(response);
    }

    return await _parseJsonResponse<T>(response);
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    throw new RequestError(errorMessage, url, undefined, error);
  }
}

async function _parseJsonResponse<T>(response: Response): Promise<T> {
  try {
    const responseData = (await response.json()) as T;

    return responseData;
  } catch (error) {
    // Response cannot be parsed, either its empty or its not a JSON
    const errorMessage =
      error instanceof Error ? error.message : "Error parsing json response";

    throw new RequestError(errorMessage, response.url, response.status, error);
  }
}

async function _parseBlobResponse<T>(response: Response): Promise<T> {
  try {
    const responseData = (await response.blob()) as T;

    return responseData;
  } catch (error) {
    // Response cannot be parsed, either its empty or its not a blob
    const errorMessage =
      error instanceof Error ? error.message : "Error parsing blob response";

    throw new RequestError(errorMessage, response.url, response.status, error);
  }
}

async function _parseTextResponse<T>(response: Response): Promise<T> {
  try {
    const responseData = (await response.text()) as T;

    return responseData;
  } catch (error) {
    // Response cannot be parsed, either its empty or its not a text
    const errorMessage =
      error instanceof Error ? error.message : "Error parsing text response";

    throw new RequestError(errorMessage, response.url, response.status, error);
  }
}

async function _handleFetchError(response: Response): Promise<never> {
  const error = await _parseJsonResponse<{ message?: string }>(response);
  const defaultErrorMessage = `HTTP error: ${response.statusText}`;

  throw new RequestError(
    error?.message ?? defaultErrorMessage,
    response.url,
    response.status,
    error
  );
}

export { fetchWrapper, type RequestOptions, type ResponseType };
