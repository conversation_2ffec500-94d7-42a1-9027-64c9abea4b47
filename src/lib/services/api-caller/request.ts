import {
  type RequestOptions,
  fetchWrapper,
  type ResponseType,
} from "./fetch-wrapper";

import { PUBLIC_API_URL } from "$env/static/public";

const request = (() => {
  async function get<T>(
    url: string,
    reponseType: ResponseType = "json",
    options?: RequestOptions
  ): Promise<T> {
    return fetchWrapper<T>(
      "GET",
      `${PUBLIC_API_URL}${url}`,
      undefined,
      reponseType,
      options
    );
  }

  async function post<T>(
    url: string,
    body: Record<string, unknown>,
    reponseType: ResponseType = "json",
    options?: RequestOptions
  ): Promise<T> {
    return fetchWrapper<T>(
      "POST",
      `${PUBLIC_API_URL}${url}`,
      body,
      reponseType,
      options
    );
  }

  async function put<T>(
    url: string,
    body: Record<string, unknown>,
    reponseType: ResponseType = "json",
    options?: RequestOptions
  ): Promise<T> {
    return fetchWrapper<T>(
      "PUT",
      `${PUBLIC_API_URL}${url}`,
      body,
      reponseType,
      options
    );
  }

  async function _delete<T>(
    url: string,
    body?: Record<string, unknown>,
    options?: RequestOptions
  ): Promise<T> {
    return fetchWrapper<T>(
      "DELETE",
      `${PUBLIC_API_URL}${url}`,
      body,
      "json",
      options
    );
  }

  return {
    get,
    post,
    put,
    delete: _delete,
  };
})();

export { request };
export { type RequestOptions } from "./fetch-wrapper";
