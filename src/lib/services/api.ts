import { PUBLIC_API_URL } from "$env/static/public";
import { authStore } from "$lib/stores/auth-store";

type ApiResponse<T> = {
  ok: boolean;
  message?: string;
  data?: T;
  statusCode?: number;
};

type RequestOptions = Omit<RequestInit, "body" | "method">;

// Note: IIFE is used to create a closure to hide the _parseResponse function.
// Also keeping functions inside not accessible to the global scope, helping
// to avoid polluting the global namespace.
// depricated: Use request.ts instead
const api = (() => {
  // eslint-disable-next-line max-params
  async function request<T>(
    method: string,
    absoluteUrl: string,
    body?: Record<string, unknown>,
    reponseType: "json" | "blob" | "text" = "json",
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    const requestOptions: RequestInit & RequestOptions = {
      method,
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: body ? JSON.stringify(body) : undefined,
      ...options,
    };

    try {
      const response = await fetch(absoluteUrl, requestOptions);

      // If we get a 401 response and it is not coming from a login request
      if (response.status === 401 && !absoluteUrl.includes("/session")) {
        // Set the logged in state to false
        authStore.set({ isLoggedIn: false });
      }

      if (reponseType === "blob") {
        return await _parseBlobResponse<T>(response);
      }

      if (reponseType === "text") {
        return await _parseTextResponse<T>(response);
      }

      return await _parseJsonResponse<T>(response);
    } catch (error) {
      return {
        ok: false,
        message: `Network Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  }

  async function get<T>(
    relativeUrl: string,
    reponseType: "json" | "blob" | "text" = "json",
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return request<T>(
      "GET",
      `${PUBLIC_API_URL}${relativeUrl}`,
      undefined,
      reponseType,
      options
    );
  }

  async function post<T>(
    relativeUrl: string,
    body: Record<string, unknown>,
    reponseType: "json" | "blob" | "text" = "json",
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return request<T>(
      "POST",
      `${PUBLIC_API_URL}${relativeUrl}`,
      body,
      reponseType,
      options
    );
  }

  async function put<T>(
    relativeUrl: string,
    body: Record<string, unknown>,
    reponseType: "json" | "blob" | "text" = "json",
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return request<T>(
      "PUT",
      `${PUBLIC_API_URL}${relativeUrl}`,
      body,
      reponseType,
      options
    );
  }

  async function _delete<T>(
    relativeUrl: string,
    body?: Record<string, unknown>,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return request<T>(
      "DELETE",
      `${PUBLIC_API_URL}${relativeUrl}`,
      body,
      "json",
      options
    );
  }

  async function _parseJsonResponse<T>(
    response: Response
  ): Promise<ApiResponse<T>> {
    try {
      const responseData = (await response.json()) as {
        data?: T;
        message?: string;
      };

      return {
        ok: response.ok,
        message: responseData?.message,
        data: responseData?.data,
        statusCode: response.status,
      };
    } catch (error) {
      // Response cannot be parsed, either its empty or its not a JSON
      const errorMessage =
        error instanceof Error ? error.message : "Error parsing json response";

      return {
        ok: false,
        message: errorMessage,
        statusCode: response.status,
      };
    }
  }

  async function _parseBlobResponse<T>(
    response: Response
  ): Promise<ApiResponse<T>> {
    try {
      const responseData = await response.blob();

      return {
        ok: response.ok,
        data: responseData as unknown as T,
        statusCode: response.status,
      };
    } catch (error) {
      // Response cannot be parsed, either its empty or its not a blob
      const errorMessage =
        error instanceof Error ? error.message : "Error parsing blob response";

      return {
        ok: false,
        message: errorMessage,
        statusCode: response.status,
      };
    }
  }

  async function _parseTextResponse<T>(
    response: Response
  ): Promise<ApiResponse<T>> {
    try {
      const responseData = await response.text();

      return {
        ok: response.ok,
        data: responseData as unknown as T,
        statusCode: response.status,
      };
    } catch (error) {
      // Response cannot be parsed, either its empty or its not a text
      const errorMessage =
        error instanceof Error ? error.message : "Error parsing text response";

      return {
        ok: false,
        message: errorMessage,
        statusCode: response.status,
      };
    }
  }

  return {
    request,
    get,
    post,
    put,
    delete: _delete,
  };
})();

export { api, type RequestOptions };
