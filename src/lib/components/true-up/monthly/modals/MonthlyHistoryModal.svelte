<script lang="ts">
  import MonthlyHistory from "../fragments/MonthlyHistory.svelte";

  import Modal from "$lib/components/ui/modals/Modal.svelte";

  export let showModal: boolean;
</script>

<Modal
  title="Monthly Settlement History"
  id="settlement-monthly-history"
  closeOnOutsideClick={false}
  size="large"
  bind:showModal
>
  <div slot="content" class="py-2">
    <MonthlyHistory />
  </div>
  <div slot="footer" />
</Modal>
