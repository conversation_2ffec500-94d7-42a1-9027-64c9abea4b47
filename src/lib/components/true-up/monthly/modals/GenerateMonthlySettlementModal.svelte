<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import MonthPicker from "$lib/components/ui/date-pickers/MonthPicker.svelte";
  import Modal from "$lib/components/ui/modals/Modal.svelte";

  export let showModal: boolean;

  let selectedDate: Date = new Date();
  selectedDate.setMonth(selectedDate.getMonth() - 1);

  const dispatch = createEventDispatcher();

  function handleConfirm() {
    dispatch("confirm", { selectedDate });
  }
</script>

<Modal
  title="Generate Monthly Settlement"
  id="generate-monthly-confirm"
  closeOnOutsideClick={false}
  bind:showModal
  on:confirm={handleConfirm}
>
  <div slot="content" class="py-6">
    <MonthPicker id={"monthly-settlement"} bind:value={selectedDate} />
  </div>
</Modal>
