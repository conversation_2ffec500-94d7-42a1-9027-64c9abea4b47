<script lang="ts">
  import { onMount } from "svelte";

  import Table from "../../../ui/table/Table.svelte";
  import {
    getTableHeaders,
    getTableRows,
    sortByMonth,
  } from "../../functions/history-table-data";
  import { type MonthlyHistoryResponse } from "../../functions/response-types";

  import {
    failureToast,
    successToast,
  } from "$lib/components/ui/notifications/toast";
  import PaginationFooter from "$lib/components/ui/pagination/PaginationFooter.svelte";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import { type Row } from "$lib/components/ui/table/types";
  import { request } from "$lib/services/api-caller/request";
  import { type RequestError } from "$lib/utils/errors/request-error";

  const headers = getTableHeaders();
  let data: MonthlyHistoryResponse[] = [];
  let selectedRows = new Set<number>();
  let tableRows: Row[] = [];
  let isLoading = false;

  let currentPage = 1;
  let recordsPerPage = 20;
  let totalCount = 0;

  const overlayActions = [
    {
      id: "delete",
      icon: "bi bi-trash",
      tooltip: "Delete",
      classes: "p-2 text-red-500 hover:text-red-700",
    },
  ];

  $: tableRows = getTableRows(data);

  $: lastPage = Math.ceil(totalCount / recordsPerPage);

  onMount(async () => {
    await loadHistory();
  });

  async function loadHistory() {
    try {
      const offset = (currentPage - 1) * recordsPerPage;
      isLoading = true;

      const queryParameters = new URLSearchParams({
        offset: offset.toString(),
        limit: recordsPerPage.toString(),
      });

      const response = await request.get<{
        jobs: MonthlyHistoryResponse[];
        totalCount: number;
      }>(`/settlement/all/monthly/history?${queryParameters.toString()}`);

      data = response?.jobs ?? [];
      totalCount = response?.totalCount ?? 0;
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError.message);
    } finally {
      isLoading = false;
    }
  }

  async function handlePageChange(event: CustomEvent<{ pageNumber: number }>) {
    currentPage = event.detail.pageNumber;
    await loadHistory();
  }

  async function handleRecordsPerPageChange(
    event: CustomEvent<{ records: number }>
  ) {
    recordsPerPage = event.detail.records;
    currentPage = 1;
    await loadHistory();
  }

  async function handleOverlayActionClick(
    event: CustomEvent<{ actionId: string }>
  ) {
    const { actionId } = event.detail;

    if (actionId === "delete") {
      try {
        isLoading = true;
        await request.delete<undefined>("/settlement/all/monthly/history", {
          jobIds: [...selectedRows],
        });
        void loadHistory();
        successToast("Settlement history deleted successfully.");
      } catch (error) {
        const requestError = error as RequestError;
        failureToast(
          requestError?.message ?? "Failed to delete settlement history"
        );
      } finally {
        isLoading = true;
      }
    }
  }

  function handleSortChange(
    event: CustomEvent<{
      id: string;
      direction: "asc" | "desc" | "none";
    }>
  ) {
    const { id, direction } = event.detail;

    if (id === "month") {
      tableRows = sortByMonth(data, direction);
    }
  }
</script>

<div class="p-2">
  {#if isLoading}
    <div class="flex-1 flex w-full h-full items-center justify-center">
      <LoadingSpinner extraClass="w-12 h-12" />
    </div>
  {:else}
    <Table
      {headers}
      rows={tableRows}
      {overlayActions}
      bind:selectedRows
      on:sortChange={handleSortChange}
      on:overlayAction={handleOverlayActionClick}
    ></Table>
  {/if}
  <PaginationFooter
    id="monthly-history-footer"
    {lastPage}
    {currentPage}
    totalNumberOfItemsListed={totalCount}
    limit={String(recordsPerPage)}
    on:pageChange={handlePageChange}
    on:recordsPerPageChange={handleRecordsPerPageChange}
  />
</div>
