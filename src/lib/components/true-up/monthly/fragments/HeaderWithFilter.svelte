<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import GenerateMonthlySettlementModal from "../modals/GenerateMonthlySettlementModal.svelte";

  import MonthlyHistoryModal from "$lib/components/true-up/monthly/modals/MonthlyHistoryModal.svelte";
  import Button from "$lib/components/ui/buttons/Button.svelte";
  import MonthPicker from "$lib/components/ui/date-pickers/MonthPicker.svelte";
  import DropDownSelector from "$lib/components/ui/drop-down/DropDownSelector.svelte";
  import Filter from "$lib/components/ui/filter/Filter.svelte";
  import TextInput from "$lib/components/ui/inputs/TextInput.svelte";
  import {
    failureToast,
    successToast,
  } from "$lib/components/ui/notifications/toast";
  import { request } from "$lib/services/api-caller/request";
  import { debounce } from "$lib/utils/debounce";
  import { type RequestError } from "$lib/utils/errors/request-error";

  import type { DropdownItem } from "$lib/types/ui/drop-down/types";

  const dispatch = createEventDispatcher();

  let isHistoryModalOpen = false;
  let isGenerateModalOpen = false;
  let textInputValue = "";
  let statusValue = "";
  let monthValue: Date | undefined;
  let hasActiveFilter = false;

  const statusItems: DropdownItem[] = [
    { label: "Success", value: "SUCCESS", colorClass: "text-green-500" },
    { label: "Skipped", value: "SKIPPED", colorClass: "text-blue-500" },
    { label: "Error", value: "ERROR", colorClass: "text-red-500" },
  ];

  function dispatchFiltersChanged() {
    dispatch("filtersChanged", {
      textInputValue,
      statusValue,
      monthValue,
    });

    hasActiveFilter = !(
      textInputValue === "" &&
      statusValue === "" &&
      monthValue === undefined
    );
  }

  function handleTextInput() {
    textInputValue = textInputValue?.trim();
    debounce(() => {
      dispatchFiltersChanged();
    }, 500);
  }

  function handleStatusChange() {
    dispatchFiltersChanged();
  }

  function handleMonthChange() {
    dispatchFiltersChanged();
  }

  function clearFilters() {
    textInputValue = "";
    statusValue = "";
    monthValue = undefined;
    dispatchFiltersChanged();
  }

  async function handleGenerateMonthlySettlementConfirm(
    event: CustomEvent<{ selectedDate: Date }>
  ) {
    const { selectedDate } = event.detail;

    if (!selectedDate) {
      failureToast("Please select a month.");

      return;
    }

    try {
      isGenerateModalOpen = false;
      const month = selectedDate.getMonth() + 1;
      const year = selectedDate.getFullYear();

      await request.post<{ jobId: number }>("/settlement/all/monthly", {
        month,
        year,
      });
      successToast("Monthly settlement generation started successfully.");
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(
        requestError?.message ?? "Failed to generate monthly settlement."
      );
    }
  }
</script>

<div>
  <Filter on:clearFilters={clearFilters} {hasActiveFilter}>
    <div slot="filter-add">
      <div class="flex gap-2 justify-end p-2">
        <div>
          <Button
            label="Monthly Settlement History"
            color="white"
            on:click={() => {
              isHistoryModalOpen = true;
            }}
          />
        </div>
        <div>
          <Button
            label="Generate Monthly Settlement"
            on:click={() => {
              isGenerateModalOpen = true;
            }}
          />
        </div>
      </div>
    </div>

    <div
      slot="content"
      class="m-4 flex flex-row content-center justify-between gap-8"
    >
      <TextInput
        id="client-filters-subject"
        label="Name / Service Number"
        placeholder="Enter a Client Name or Service Number"
        classes="flex-1"
        bind:value={textInputValue}
        on:input={handleTextInput}
      />
      <div class="flex-1">
        <DropDownSelector
          id="monthly-settlement-filters-entity"
          label="Status"
          placeholder="Select a Status"
          items={statusItems}
          clearable={true}
          on:clear={() => {
            statusValue = "";
            handleStatusChange();
          }}
          labelText={statusValue}
          bind:value={statusValue}
          on:change={handleStatusChange}
        />
      </div>
      <div class="flex-1">
        <div class="mb-1.5 text-sm font-medium text-gray-800">Month</div>
        <MonthPicker
          id={"monthly-filter"}
          bind:value={monthValue}
          on:change={handleMonthChange}
        />
      </div>
    </div>
  </Filter>

  <GenerateMonthlySettlementModal
    bind:showModal={isGenerateModalOpen}
    on:confirm={handleGenerateMonthlySettlementConfirm}
  />
  <MonthlyHistoryModal bind:showModal={isHistoryModalOpen} />
</div>
