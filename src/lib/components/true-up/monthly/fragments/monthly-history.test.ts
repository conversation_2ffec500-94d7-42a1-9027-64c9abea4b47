import { fireEvent, render, screen } from "@testing-library/svelte";
import { tick } from "svelte";
import { describe, it, expect, vi, type MockedFunction } from "vitest";

import MonthlyHistory from "./MonthlyHistory.svelte";

import { request } from "$lib/services/api-caller/request";
import { formatDate } from "$lib/utils/date-utils";

vi.mock("$lib/services/api-caller/request", () => ({
  request: {
    get: vi.fn(),
    delete: vi.fn(),
  },
}));

describe("MonthlyHistory", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("should render correctly with no data", async () => {
    render(MonthlyHistory);
    await tick();

    expect(request.get).toHaveBeenCalledTimes(1);
    expect(request.get).toHaveBeenCalledWith(
      "/settlement/all/monthly/history?offset=0&limit=20"
    );

    expect(screen.getByText("Month")).toBeDefined();
    expect(screen.getByText("Generated By")).toBeDefined();
    expect(screen.getByText("Status")).toBeDefined();
    expect(screen.getByText("Stats")).toBeDefined();
    expect(screen.getByTestId("page-1")).toBeDefined();
  });

  it("should render correctly with provided data", async () => {
    const mockResponse = {
      jobs: [
        {
          jobId: 1,
          month: 1,
          year: 2024,
          status: "SUCCESS",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:42:16.000Z",
          meta: {
            errorCount: 0,
            totalCount: 499,
            skippedCount: 191,
            successCount: 308,
            timeTakenInSeconds: 361.81,
          },
        },
        {
          jobId: 2,
          month: 2,
          year: 2024,
          status: "PROGRESS",
          userId: 12,
          userName: "Test User2",
          generatedAt: "2024-12-18T21:21:13.000Z",
          meta: {},
        },
        {
          jobId: 3,
          month: 3,
          year: 2024,
          status: "ERROR",
          userId: 13,
          userName: "Test User3",
          generatedAt: "2024-12-20T00:18:06.000Z",
          meta: {},
        },
      ],
      totalCount: 3,
    };

    (request.get as MockedFunction<typeof request.get>).mockResolvedValue(
      mockResponse
    );

    render(MonthlyHistory);
    await tick();

    expect(request.get).toHaveBeenCalledTimes(1);
    expect(request.get).toHaveBeenCalledWith(
      "/settlement/all/monthly/history?offset=0&limit=20"
    );

    // Success job
    expect(screen.getByText("1-2024")).toBeDefined();
    expect(screen.getByText("Test User1")).toBeDefined();
    expect(
      screen.getByText(formatDate(new Date("2024-12-17T22:42:16.000Z")))
    ).toBeDefined();
    expect(screen.getByText("Duration: 361.81 seconds")).toBeDefined();
    expect(screen.getByText("SUCCESS")).toBeDefined();
    expect(screen.getByText("Success: 308")).toBeDefined();
    expect(screen.getByText("Total: 499")).toBeDefined();
    expect(screen.getByText("Skipped: 191")).toBeDefined();
    expect(screen.getByText("Error: 0")).toBeDefined();

    // Progress job
    expect(screen.getByText("2-2024")).toBeDefined();
    expect(screen.getByText("Test User2")).toBeDefined();
    expect(
      screen.getByText(formatDate(new Date("2024-12-18T21:21:13.000Z")))
    ).toBeDefined();
    expect(screen.getByText("PROGRESS")).toBeDefined();

    // Error job
    expect(screen.getByText("3-2024")).toBeDefined();
    expect(screen.getByText("Test User3")).toBeDefined();
    expect(
      screen.getByText(formatDate(new Date("2024-12-20T00:18:06.000Z")))
    ).toBeDefined();
    expect(screen.getByText("ERROR")).toBeDefined();

    // Total records
    expect(screen.getByText("1 - 3 / 3")).toBeDefined();
  });

  it("should have two pages when there are more than selected records per page", async () => {
    const mockResponse = {
      jobs: [
        {
          jobId: 1,
          month: 1,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:42:16.000Z",
          meta: {},
        },
        {
          jobId: 2,
          month: 2,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:43:16.000Z",
          meta: {},
        },
        {
          jobId: 3,
          month: 3,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:44:16.000Z",
          meta: {},
        },
        {
          jobId: 4,
          month: 4,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:45:16.000Z",
          meta: {},
        },
        {
          jobId: 5,
          month: 5,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:46:16.000Z",
          meta: {},
        },
        {
          jobId: 6,
          month: 6,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:47:16.000Z",
          meta: {},
        },
        {
          jobId: 7,
          month: 7,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:48:16.000Z",
          meta: {},
        },
        {
          jobId: 8,
          month: 8,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:49:16.000Z",
          meta: {},
        },
        {
          jobId: 9,
          month: 9,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:50:16.000Z",
          meta: {},
        },
        {
          jobId: 10,
          month: 10,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:51:16.000Z",
          meta: {},
        },
        {
          jobId: 11,
          month: 11,
          year: 2024,
          status: "ERROR",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:52:16.000Z",
          meta: {},
        },
      ],
      totalCount: 11,
    };

    (request.get as MockedFunction<typeof request.get>).mockResolvedValue(
      mockResponse
    );

    render(MonthlyHistory);
    await tick();

    expect(request.get).toHaveBeenCalledTimes(1);
    expect(request.get).toHaveBeenCalledWith(
      "/settlement/all/monthly/history?offset=0&limit=20"
    );

    const recordsSelector = screen.getByDisplayValue("20");
    await fireEvent.change(recordsSelector, { target: { value: "10" } });

    expect(screen.getByText("1 - 10 / 11")).toBeDefined();
    expect(request.get).toHaveBeenCalledTimes(2);
    expect(request.get).toHaveBeenCalledWith(
      "/settlement/all/monthly/history?offset=0&limit=10"
    );

    const pageTwo = screen.getByTestId("page-2");
    expect(pageTwo).toBeDefined();
    await fireEvent.click(pageTwo);

    expect(screen.getByText("11 - 11 / 11")).toBeDefined();
    expect(request.get).toHaveBeenCalledTimes(3);
    expect(request.get).toHaveBeenCalledWith(
      "/settlement/all/monthly/history?offset=10&limit=10"
    );
  });

  it("should be able to select records", async () => {
    const mockResponse = {
      jobs: [
        {
          jobId: 1,
          month: 1,
          year: 2024,
          status: "SUCCESS",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:42:16.000Z",
          meta: {
            errorCount: 0,
            totalCount: 499,
            skippedCount: 191,
            successCount: 308,
            timeTakenInSeconds: 361.81,
          },
        },
        {
          jobId: 2,
          month: 2,
          year: 2024,
          status: "PROGRESS",
          userId: 12,
          userName: "Test User2",
          generatedAt: "2024-12-18T21:21:13.000Z",
          meta: {},
        },
        {
          jobId: 3,
          month: 3,
          year: 2024,
          status: "ERROR",
          userId: 13,
          userName: "Test User3",
          generatedAt: "2024-12-20T00:18:06.000Z",
          meta: {},
        },
      ],
      totalCount: 3,
    };

    (request.get as MockedFunction<typeof request.get>).mockResolvedValue(
      mockResponse
    );

    render(MonthlyHistory);
    await tick();

    expect(request.get).toHaveBeenCalledTimes(1);
    expect(request.get).toHaveBeenCalledWith(
      "/settlement/all/monthly/history?offset=0&limit=20"
    );

    const checkboxes = screen.getAllByRole("checkbox");

    const selectAllCheckbox = checkboxes[0];
    await fireEvent.click(selectAllCheckbox);
    expect(screen.getByText("3 selected")).toBeDefined();

    await fireEvent.click(selectAllCheckbox);
    expect(screen.queryByText("3 selected")).toBeNull();

    const firstRecordCheckbox = checkboxes[1];
    await fireEvent.click(firstRecordCheckbox);
    expect(screen.getByText("1 selected")).toBeDefined();

    const secondRecordCheckbox = checkboxes[2];
    await fireEvent.click(secondRecordCheckbox);
    expect(screen.getByText("2 selected")).toBeDefined();

    const thirdRecordCheckbox = checkboxes[3];
    await fireEvent.click(thirdRecordCheckbox);
    expect(screen.getByText("3 selected")).toBeDefined();
  });

  it("should send delete request when delete button is clicked", async () => {
    const mockResponse = {
      jobs: [
        {
          jobId: 1,
          month: 1,
          year: 2024,
          status: "SUCCESS",
          userId: 11,
          userName: "Test User1",
          generatedAt: "2024-12-17T22:42:16.000Z",
          meta: {
            errorCount: 0,
            totalCount: 499,
            skippedCount: 191,
            successCount: 308,
            timeTakenInSeconds: 361.81,
          },
        },
        {
          jobId: 2,
          month: 2,
          year: 2024,
          status: "PROGRESS",
          userId: 12,
          userName: "Test User2",
          generatedAt: "2024-12-18T21:21:13.000Z",
          meta: {},
        },
        {
          jobId: 3,
          month: 3,
          year: 2024,
          status: "ERROR",
          userId: 13,
          userName: "Test User3",
          generatedAt: "2024-12-20T00:18:06.000Z",
          meta: {},
        },
      ],
      totalCount: 3,
    };

    (request.get as MockedFunction<typeof request.get>).mockResolvedValue(
      mockResponse
    );

    render(MonthlyHistory);
    await tick();

    expect(request.get).toHaveBeenCalledTimes(1);
    expect(request.get).toHaveBeenCalledWith(
      "/settlement/all/monthly/history?offset=0&limit=20"
    );

    const checkboxes = screen.getAllByRole("checkbox");

    const firstRecordCheckbox = checkboxes[1];
    await fireEvent.click(firstRecordCheckbox);

    const secondRecordCheckbox = checkboxes[2];
    await fireEvent.click(secondRecordCheckbox);

    const thirdRecordCheckbox = checkboxes[3];
    await fireEvent.click(thirdRecordCheckbox);

    const deleteButton = screen.getByLabelText("Delete");
    await fireEvent.click(deleteButton);

    expect(request.delete).toHaveBeenCalledTimes(1);
    expect(request.delete).toHaveBeenCalledWith(
      "/settlement/all/monthly/history",
      {
        jobIds: [1, 2, 3],
      }
    );

    expect(request.get).toHaveBeenCalledTimes(2);
    expect(request.get).toHaveBeenCalledWith(
      "/settlement/all/monthly/history?offset=0&limit=20"
    );
  });
});
