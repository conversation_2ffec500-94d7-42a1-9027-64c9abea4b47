<script lang="ts">
  import { onMount } from "svelte";

  import HeaderWithFilter from "./fragments/HeaderWithFilter.svelte";
  import RecalculateConfirmModal from "./modals/RecalculateConfirmModal.svelte";
  import Table from "../../ui/table/Table.svelte";
  import {
    getTableRows,
    getTableHeaders,
    getOverlayActions,
  } from "../functions/monthly-table-data";
  import { type MonthlySettlementResponse } from "../functions/response-types";

  import {
    failureToast,
    successToast,
  } from "$lib/components/ui/notifications/toast";
  import PaginationFooter from "$lib/components/ui/pagination/PaginationFooter.svelte";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import { type Row, type OverlayAction } from "$lib/components/ui/table/types";
  import { request } from "$lib/services/api-caller/request";
  import { type RequestError } from "$lib/utils/errors/request-error";

  const selectedDate: Date = new Date();
  selectedDate.setMonth(selectedDate.getMonth() - 1);

  const headers = getTableHeaders();
  let sourceDataArray: MonthlySettlementResponse[] = [];
  let tableRows: Row[] = [];
  let overlayActions: OverlayAction[] = [];
  let selectedRows = new Set<number>();
  let isLoading = false;

  let currentPage = 1;
  let recordsPerPage = 20;
  let totalCount = 0;

  let filters: Partial<{
    month: number | undefined;
    year: number | undefined;
    status: string | undefined;
    customerName: string | undefined;
    serviceNumber: string | undefined;
  }> = {};

  let isRecalculateModalOpen = false;

  $: sourceSelectedRow = sourceDataArray.find(
    (record) => record.id === selectedRows.values().next().value
  );

  $: lastPage = Math.ceil(totalCount / recordsPerPage);

  async function loadMonthlySettlements() {
    try {
      const offset = (currentPage - 1) * recordsPerPage;
      isLoading = true;
      const response = await request.post<{
        monthlySettlements: MonthlySettlementResponse[];
        totalCount: number;
      }>("/settlement/all/monthly/read", {
        offset,
        limit: recordsPerPage,
        ...filters,
      });

      sourceDataArray = response?.monthlySettlements ?? [];
      tableRows = getTableRows(sourceDataArray);
      totalCount = response?.totalCount ?? 0;
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(
        requestError?.message ?? "Failed to fetch monthly settlements"
      );
    } finally {
      isLoading = false;
    }
  }

  async function handleRecordsPerPageChange(
    event: CustomEvent<{ records: number }>
  ) {
    recordsPerPage = event.detail.records;
    currentPage = 1;
    await loadMonthlySettlements();
  }
  async function handlePageChange(event: CustomEvent<{ pageNumber: number }>) {
    currentPage = event.detail.pageNumber;
    await loadMonthlySettlements();
  }

  async function handleOverlayActionClick(
    event: CustomEvent<{ actionId: string }>
  ) {
    const { actionId } = event.detail;

    if (actionId === "recalculate") {
      isRecalculateModalOpen = true;
    }
  }

  async function handleRecalculateConfirmClick() {
    try {
      const monthlyCustomerSettlementId = selectedRows.values().next()
        .value as number;

      isRecalculateModalOpen = false;
      isLoading = true;
      await request.post<undefined>("/settlement/all/monthly/recalculate", {
        monthlyCustomerSettlementId,
      });
      await loadMonthlySettlements();
      successToast("Monthly settlement recalculated successfully");
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(
        requestError?.message ?? "Failed to recalculate monthly settlement"
      );
    } finally {
      isLoading = false;
    }
  }

  async function handleFilterChange(
    event: CustomEvent<{
      textInputValue: string;
      statusValue: string;
      monthValue: Date;
    }>
  ) {
    const { textInputValue, statusValue, monthValue } = event.detail;

    let month;
    let year;

    if (monthValue) {
      month = monthValue.getMonth() + 1;
      year = monthValue.getFullYear();
    }

    const status = statusValue ? statusValue.toUpperCase() : undefined;
    filters = {
      month,
      year,
      status,
      customerName: textInputValue,
      serviceNumber: textInputValue,
    };

    await loadMonthlySettlements();
  }

  onMount(async () => {
    currentPage = 1;
    await loadMonthlySettlements();
    overlayActions = getOverlayActions();
  });
</script>

<div class="flex flex-col flex-grow min-h-0">
  <HeaderWithFilter on:filtersChanged={handleFilterChange} />
  <div class="flex flex-col flex-grow min-h-0">
    {#if isLoading}
      <div class="flex-1 flex w-full h-full items-center justify-center">
        <LoadingSpinner extraClass="w-12 h-12" />
      </div>
    {:else}
      <div class="flex-grow overflow-y-auto scrollbar-hide">
        <Table
          {headers}
          rows={tableRows}
          {overlayActions}
          bind:selectedRows
          on:overlayAction={handleOverlayActionClick}
        ></Table>
      </div>
    {/if}
    <PaginationFooter
      id="monthly-settlement-footer"
      {lastPage}
      {currentPage}
      totalNumberOfItemsListed={totalCount}
      limit={String(recordsPerPage)}
      on:pageChange={handlePageChange}
      on:recordsPerPageChange={handleRecordsPerPageChange}
    />
  </div>
  <RecalculateConfirmModal
    bind:showModal={isRecalculateModalOpen}
    on:confirm={handleRecalculateConfirmClick}
    customerName={sourceSelectedRow
      ? `${sourceSelectedRow?.customerName} / ${sourceSelectedRow?.serviceNumber}`
      : ""}
    period={sourceSelectedRow
      ? `${sourceSelectedRow?.month}-${sourceSelectedRow?.year} ?`
      : ""}
  />
</div>
