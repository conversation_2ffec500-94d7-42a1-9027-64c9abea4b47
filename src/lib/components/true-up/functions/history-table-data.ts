import { type MonthlyHistoryResponse } from "./response-types";
import { getStatusColor } from "./status-color";
import { formatDate } from "../../../utils/date-utils";
import ListText from "../../ui/table/common-cell-components/ListText.svelte";
import Badge from "../../ui/tools/Badge.svelte";

import { type Row, type Header } from "$lib/components/ui/table/types";

const getTableRows = (rows: MonthlyHistoryResponse[]): Row[] => {
  const tableRows: Row[] = [];

  for (const item of rows) {
    const newRow: Row = {
      id: item.jobId,
      showCheckbox: true,
      cells: [
        `${item.month}-${item.year}`,
        {
          component: ListText,
          props: {
            lines: [
              [{ text: item.userName }],
              [
                {
                  text: formatDate(new Date(item.generatedAt)),
                  classes: "text-gray-500 text-sm",
                },
              ],
              item?.meta?.timeTakenInSeconds && [
                {
                  text: `Duration: ${item.meta.timeTakenInSeconds.toFixed(2)} seconds`,
                  classes: "text-yellow-900 text-sm",
                },
              ],
            ],
          },
        },
        {
          component: Badge,
          props: {
            label: item.status,
            color: getStatusColor(item.status),
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              item?.meta &&
                item.status === "SUCCESS" && [
                  {
                    text: `Success: ${item.meta.successCount}`,
                    classes: "text-green-500",
                  },
                  {
                    text: `Total: ${item.meta.totalCount}`,
                    classes: "text-blue-500",
                  },
                ],
              item?.meta &&
                item.status === "SUCCESS" && [
                  {
                    text: `Skipped: ${item.meta.skippedCount}`,
                    classes: "text-yellow-500",
                  },
                  {
                    text: `Error: ${item.meta.errorCount}`,
                    classes: "text-red-500",
                  },
                ],
            ],
          },
        },
      ],
    };
    tableRows.push(newRow);
  }

  return tableRows;
};

const getTableHeaders = (): Header[] => {
  return [
    { id: "month", title: "Month", align: "left", sortable: true },
    { id: "generateBy", title: "Generated By", align: "left" },
    { id: "status", title: "Status", align: "left" },
    { id: "stats", title: "Stats", align: "left" },
  ] as Header[];
};

const sortByMonth = (
  rows: MonthlyHistoryResponse[],
  direction: "asc" | "desc" | "none"
): Row[] => {
  if (direction === "none") {
    return getTableRows(rows);
  }

  const sorted = rows.sort((a, b) => {
    if (a.year === b.year) {
      return a.month - b.month;
    }

    return a.year - b.year;
  });

  if (direction === "desc") {
    sorted.reverse();
  }

  return getTableRows(sorted);
};

export { getTableRows, getTableHeaders, sortByMonth };
