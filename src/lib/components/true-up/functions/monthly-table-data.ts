import { type MonthlySettlementResponse } from "./response-types";
import { getStatusColor } from "./status-color";
import Badge from "../../ui/tools/Badge.svelte";

import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
import {
  type Header,
  type OverlayAction,
  type Row,
} from "$lib/components/ui/table/types";
import { formatDate } from "$lib/utils/date-utils";

const getTableRows = (rows: MonthlySettlementResponse[]): Row[] => {
  const tableRows: Row[] = [];

  for (const item of rows) {
    const newRow: Row = {
      id: item.id,
      cells: [
        {
          component: ListText,
          props: {
            lines: [
              [{ text: item.customerName }],
              [
                {
                  text: item.serviceNumber,
                  classes: "text-gray-500 text-sm",
                },
              ],
            ],
          },
        },
        `${item.month}-${item.year}`,
        {
          component: Badge,
          props: {
            label: item.status,
            color: getStatusColor(item.status),
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [
                {
                  text: formatDate(new Date(item.updatedAt)),
                  classes: "text-gray-500 text-sm",
                },
              ],
              item?.timeTakenInSeconds && [
                {
                  text: `Duration: ${item.timeTakenInSeconds.toFixed(2)} seconds`,
                  classes: "text-yellow-900 text-sm",
                },
              ],
            ],
          },
        },
        item.status === "SUCCESS" && item.adjustmentTotal
          ? `totalAdjustment: ${item.adjustmentTotal}`
          : (item.message ?? ""),
      ],
    };
    tableRows.push(newRow);
  }

  return tableRows;
};

const getTableHeaders = (): Header[] => {
  return [
    { id: "Client", title: "Client", align: "left" },
    { id: "month", title: "Month", align: "left" },
    { id: "status", title: "Status", align: "left" },
    { id: "updated", title: "Updated At", align: "left" },
    { id: "message", title: "Message", align: "center" },
  ];
};

const getOverlayActions = (): OverlayAction[] => {
  return [
    {
      id: "recalculate",
      text: "Recalculate",
      classes: "text-blue-500 bg-blue-100 p-2 rounded-md hover:bg-blue-200",
    },
  ];
};

export { getTableRows, getTableHeaders, getOverlayActions };
