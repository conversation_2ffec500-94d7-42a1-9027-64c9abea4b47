type MonthlyHistoryResponse = {
  jobId: number;
  month: number;
  year: number;
  status: string;
  userId: number;
  userName: string;
  generatedAt: string;
  meta?: {
    totalCount: number;
    successCount: number;
    skippedCount: number;
    errorCount: number;
    timeTakenInSeconds: number;
  };
};

type MonthlySettlementResponse = {
  id: number;
  customerName: string;
  serviceNumber: string;
  month: number;
  year: number;
  status: string;
  adjustmentTotal: number;
  updatedAt: string;
  timeTakenInSeconds?: number;
  message?: string;
};

export { type MonthlyHistoryResponse, type MonthlySettlementResponse };
