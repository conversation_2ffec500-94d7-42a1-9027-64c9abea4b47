import { describe, it, expect } from "vitest";

import { getTableRows, sortByMonth } from "./history-table-data";

import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
import Badge from "$lib/components/ui/tools/Badge.svelte";
import { formatDate } from "$lib/utils/date-utils";

describe("getTableRows", () => {
  it("should return an empty array when empty array is provided", () => {
    const result = getTableRows([]);
    expect(result).toEqual([]);
  });

  it("should return an array of rows with all data when status is success", () => {
    const mockData = [
      {
        jobId: 123,
        month: 1,
        year: 2024,
        status: "SUCCESS",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-10-11T14:58:13.000Z",
        meta: {
          errorCount: 0,
          totalCount: 499,
          skippedCount: 191,
          successCount: 308,
          timeTakenInSeconds: 361.81,
        },
      },
      {
        jobId: 456,
        month: 2,
        year: 2024,
        status: "SUCCESS",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-10-11T17:29:06.000Z",
        meta: {
          errorCount: 0,
          totalCount: 499,
          skippedCount: 192,
          successCount: 307,
          timeTakenInSeconds: 348.03,
        },
      },
    ];

    const result = getTableRows(mockData);
    expect(result).toEqual([
      {
        id: 123,
        showCheckbox: true,
        cells: [
          "1-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-10-11T14:58:13.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                [
                  {
                    text: "Duration: 361.81 seconds",
                    classes: "text-yellow-900 text-sm",
                  },
                ],
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "SUCCESS",
              color: "green",
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [
                  {
                    text: "Success: 308",
                    classes: "text-green-500",
                  },
                  {
                    text: "Total: 499",
                    classes: "text-blue-500",
                  },
                ],
                [
                  {
                    text: "Skipped: 191",
                    classes: "text-yellow-500",
                  },
                  {
                    text: "Error: 0",
                    classes: "text-red-500",
                  },
                ],
              ],
            },
          },
        ],
      },
      {
        id: 456,
        showCheckbox: true,
        cells: [
          "2-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-10-11T17:29:06.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                [
                  {
                    text: "Duration: 348.03 seconds",
                    classes: "text-yellow-900 text-sm",
                  },
                ],
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "SUCCESS",
              color: "green",
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [
                  {
                    text: "Success: 307",
                    classes: "text-green-500",
                  },
                  {
                    text: "Total: 499",
                    classes: "text-blue-500",
                  },
                ],
                [
                  {
                    text: "Skipped: 192",
                    classes: "text-yellow-500",
                  },
                  {
                    text: "Error: 0",
                    classes: "text-red-500",
                  },
                ],
              ],
            },
          },
        ],
      },
    ]);
  });

  it("should return an array of rows without any meta data when status is progress or error and meta is empty object", () => {
    const mockData = [
      {
        jobId: 64,
        month: 11,
        year: 2024,
        status: "PROGRESS",
        userId: 41,
        userName: "Test User",
        generatedAt: "2024-12-11T21:21:13.000Z",
        meta: {},
      },
      {
        jobId: 65,
        month: 11,
        year: 2024,
        status: "ERROR",
        userId: 41,
        userName: "Test User",
        generatedAt: "2024-12-13T19:53:39.000Z",
        meta: {},
      },
    ];

    // @ts-expect-error - backend may return empty object for meta
    const result = getTableRows(mockData);
    expect(result).toEqual([
      {
        id: 64,
        showCheckbox: true,
        cells: [
          "11-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-12-11T21:21:13.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                undefined,
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "PROGRESS",
              color: "orange",
            },
          },
          {
            component: ListText,
            props: {
              lines: [false, false],
            },
          },
        ],
      },
      {
        id: 65,
        showCheckbox: true,
        cells: [
          "11-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-12-13T19:53:39.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                undefined,
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "ERROR",
              color: "red",
            },
          },
          {
            component: ListText,
            props: {
              lines: [false, false],
            },
          },
        ],
      },
    ]);
  });

  it("should return an array of rows without any meta data when status is progress or error and meta is null/undefined", () => {
    const mockData = [
      {
        jobId: 64,
        month: 11,
        year: 2024,
        status: "PROGRESS",
        userId: 41,
        userName: "Test User",
        generatedAt: "2024-12-11T21:21:13.000Z",
        // eslint-disable-next-line unicorn/no-null
        meta: null,
      },
      {
        jobId: 65,
        month: 11,
        year: 2024,
        status: "ERROR",
        userId: 41,
        userName: "Test User",
        generatedAt: "2024-12-13T19:53:39.000Z",
        meta: undefined,
      },
    ];

    // @ts-expect-error - backend may return empty object for meta
    const result = getTableRows(mockData);
    expect(result).toEqual([
      {
        id: 64,
        showCheckbox: true,
        cells: [
          "11-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-12-11T21:21:13.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                undefined,
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "PROGRESS",
              color: "orange",
            },
          },
          {
            component: ListText,
            props: {
              // eslint-disable-next-line unicorn/no-null
              lines: [null, null],
            },
          },
        ],
      },
      {
        id: 65,
        showCheckbox: true,
        cells: [
          "11-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-12-13T19:53:39.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                undefined,
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "ERROR",
              color: "red",
            },
          },
          {
            component: ListText,
            props: {
              lines: [undefined, undefined],
            },
          },
        ],
      },
    ]);
  });
});

describe("sortByMonth", () => {
  it("should sort the data by month ascending when direction is asc", () => {
    const mockData = [
      {
        jobId: 11,
        month: 1,
        year: 2024,
        status: "ERROR",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-01-22T20:21:22.000Z",
        meta: {},
      },
      {
        jobId: 22,
        month: 2,
        year: 2024,
        status: "SUCCESS",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-02-29T21:29:29.000Z",
        meta: {
          errorCount: 0,
          totalCount: 499,
          skippedCount: 191,
          successCount: 308,
          timeTakenInSeconds: 361.81,
        },
      },
      {
        jobId: 33,
        month: 1,
        year: 2023,
        status: "SUCCESS",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-03-11T11:45:07.000Z",
        meta: {
          errorCount: 0,
          totalCount: 499,
          skippedCount: 192,
          successCount: 307,
          timeTakenInSeconds: 348.03,
        },
      },
    ];

    // @ts-expect-error - backend may return empty object for meta
    const result = sortByMonth(mockData, "asc");
    expect(result).toEqual([
      {
        id: 33,
        showCheckbox: true,
        cells: [
          "1-2023",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-03-11T11:45:07.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                [
                  {
                    text: "Duration: 348.03 seconds",
                    classes: "text-yellow-900 text-sm",
                  },
                ],
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "SUCCESS",
              color: "green",
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [
                  {
                    text: "Success: 307",
                    classes: "text-green-500",
                  },
                  {
                    text: "Total: 499",
                    classes: "text-blue-500",
                  },
                ],
                [
                  {
                    text: "Skipped: 192",
                    classes: "text-yellow-500",
                  },
                  {
                    text: "Error: 0",
                    classes: "text-red-500",
                  },
                ],
              ],
            },
          },
        ],
      },
      {
        id: 11,
        showCheckbox: true,
        cells: [
          "1-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-01-22T20:21:22.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                undefined,
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "ERROR",
              color: "red",
            },
          },
          {
            component: ListText,
            props: {
              lines: [false, false],
            },
          },
        ],
      },
      {
        id: 22,
        showCheckbox: true,
        cells: [
          "2-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-02-29T21:29:29.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                [
                  {
                    text: "Duration: 361.81 seconds",
                    classes: "text-yellow-900 text-sm",
                  },
                ],
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "SUCCESS",
              color: "green",
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [
                  {
                    text: "Success: 308",
                    classes: "text-green-500",
                  },
                  {
                    text: "Total: 499",
                    classes: "text-blue-500",
                  },
                ],
                [
                  {
                    text: "Skipped: 191",
                    classes: "text-yellow-500",
                  },
                  {
                    text: "Error: 0",
                    classes: "text-red-500",
                  },
                ],
              ],
            },
          },
        ],
      },
    ]);
  });

  it("should sort the data by month descending when direction is desc", () => {
    const mockData = [
      {
        jobId: 11,
        month: 1,
        year: 2024,
        status: "ERROR",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-01-22T20:21:22.000Z",
        meta: {},
      },
      {
        jobId: 22,
        month: 2,
        year: 2024,
        status: "SUCCESS",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-02-29T21:29:29.000Z",
        meta: {
          errorCount: 0,
          totalCount: 499,
          skippedCount: 191,
          successCount: 308,
          timeTakenInSeconds: 361.81,
        },
      },
      {
        jobId: 33,
        month: 1,
        year: 2023,
        status: "SUCCESS",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-03-11T11:45:07.000Z",
        meta: {
          errorCount: 0,
          totalCount: 499,
          skippedCount: 192,
          successCount: 307,
          timeTakenInSeconds: 348.03,
        },
      },
    ];

    // @ts-expect-error - backend may return empty object for meta
    const result = sortByMonth(mockData, "desc");
    expect(result).toEqual([
      {
        id: 22,
        showCheckbox: true,
        cells: [
          "2-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-02-29T21:29:29.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                [
                  {
                    text: "Duration: 361.81 seconds",
                    classes: "text-yellow-900 text-sm",
                  },
                ],
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "SUCCESS",
              color: "green",
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [
                  {
                    text: "Success: 308",
                    classes: "text-green-500",
                  },
                  {
                    text: "Total: 499",
                    classes: "text-blue-500",
                  },
                ],
                [
                  {
                    text: "Skipped: 191",
                    classes: "text-yellow-500",
                  },
                  {
                    text: "Error: 0",
                    classes: "text-red-500",
                  },
                ],
              ],
            },
          },
        ],
      },
      {
        id: 11,
        showCheckbox: true,
        cells: [
          "1-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-01-22T20:21:22.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                undefined,
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "ERROR",
              color: "red",
            },
          },
          {
            component: ListText,
            props: {
              lines: [false, false],
            },
          },
        ],
      },
      {
        id: 33,
        showCheckbox: true,
        cells: [
          "1-2023",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-03-11T11:45:07.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                [
                  {
                    text: "Duration: 348.03 seconds",
                    classes: "text-yellow-900 text-sm",
                  },
                ],
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "SUCCESS",
              color: "green",
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [
                  {
                    text: "Success: 307",
                    classes: "text-green-500",
                  },
                  {
                    text: "Total: 499",
                    classes: "text-blue-500",
                  },
                ],
                [
                  {
                    text: "Skipped: 192",
                    classes: "text-yellow-500",
                  },
                  {
                    text: "Error: 0",
                    classes: "text-red-500",
                  },
                ],
              ],
            },
          },
        ],
      },
    ]);
  });

  it("should not sort the data when direction is none", () => {
    const mockData = [
      {
        jobId: 11,
        month: 1,
        year: 2024,
        status: "ERROR",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-01-22T20:21:22.000Z",
        meta: {},
      },
      {
        jobId: 22,
        month: 2,
        year: 2024,
        status: "SUCCESS",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-02-29T21:29:29.000Z",
        meta: {
          errorCount: 0,
          totalCount: 499,
          skippedCount: 191,
          successCount: 308,
          timeTakenInSeconds: 361.81,
        },
      },
      {
        jobId: 33,
        month: 1,
        year: 2023,
        status: "SUCCESS",
        userId: 11,
        userName: "Test User",
        generatedAt: "2024-03-11T11:45:07.000Z",
        meta: {
          errorCount: 0,
          totalCount: 499,
          skippedCount: 192,
          successCount: 307,
          timeTakenInSeconds: 348.03,
        },
      },
    ];

    // @ts-expect-error - backend may return empty object for meta
    const result = sortByMonth(mockData, "none");
    expect(result).toEqual([
      {
        id: 11,
        showCheckbox: true,
        cells: [
          "1-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-01-22T20:21:22.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                undefined,
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "ERROR",
              color: "red",
            },
          },
          {
            component: ListText,
            props: {
              lines: [false, false],
            },
          },
        ],
      },
      {
        id: 22,
        showCheckbox: true,
        cells: [
          "2-2024",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-02-29T21:29:29.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                [
                  {
                    text: "Duration: 361.81 seconds",
                    classes: "text-yellow-900 text-sm",
                  },
                ],
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "SUCCESS",
              color: "green",
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [
                  {
                    text: "Success: 308",
                    classes: "text-green-500",
                  },
                  {
                    text: "Total: 499",
                    classes: "text-blue-500",
                  },
                ],
                [
                  {
                    text: "Skipped: 191",
                    classes: "text-yellow-500",
                  },
                  {
                    text: "Error: 0",
                    classes: "text-red-500",
                  },
                ],
              ],
            },
          },
        ],
      },
      {
        id: 33,
        showCheckbox: true,
        cells: [
          "1-2023",
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Test User" }],
                [
                  {
                    text: formatDate(new Date("2024-03-11T11:45:07.000Z")),
                    classes: "text-gray-500 text-sm",
                  },
                ],
                [
                  {
                    text: "Duration: 348.03 seconds",
                    classes: "text-yellow-900 text-sm",
                  },
                ],
              ],
            },
          },
          {
            component: Badge,
            props: {
              label: "SUCCESS",
              color: "green",
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [
                  {
                    text: "Success: 307",
                    classes: "text-green-500",
                  },
                  {
                    text: "Total: 499",
                    classes: "text-blue-500",
                  },
                ],
                [
                  {
                    text: "Skipped: 192",
                    classes: "text-yellow-500",
                  },
                  {
                    text: "Error: 0",
                    classes: "text-red-500",
                  },
                ],
              ],
            },
          },
        ],
      },
    ]);
  });
});
