<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import SettlementCard from "./SettlementCard.svelte";
  import ButtonNew from "../ui/buttons/ButtonNew.svelte";
  import Checkbox from "../ui/inputs/Checkbox.svelte";
  import SelectionOverlay from "../ui/table/SelectionOverlay.svelte";

  import type { OverlayAction } from "$lib/components/ui/table/types";
  import type { SettlementSummary } from "$lib/types/settlement/types";

  export let loadedSettlementSummary: SettlementSummary[] = [];
  export let selectedRows = new Set<string>();
  export let overlayActions: OverlayAction[] = [
    {
      id: "approve",
      text: "Approve",
      classes:
        "dark:bg-neutral-800 dark:border-neutral-600 text-green-500 p-2 border border-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-neutral-700",
    },
    {
      id: "regenerate",
      text: "Regenerate",
      classes:
        "dark:bg-neutral-800 dark:border-neutral-600 text-blue-500 p-2 border border-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-neutral-700",
    },
    {
      id: "delete",
      text: "Delete",
      classes:
        "dark:bg-neutral-800 dark:border-neutral-600 text-rose-500 p-2 border border-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-neutral-700",
    },
  ];

  $: allSelected = loadedSettlementSummary.every((settlement) =>
    selectedRows.has(settlement.id)
  );

  const dispatch = createEventDispatcher();

  const toggleSelectRow = (event: CustomEvent<{ id: string }>) => {
    const newSelectedRows = new Set(selectedRows);
    const isSelected = newSelectedRows.has(event.detail.id);

    if (isSelected) {
      newSelectedRows.delete(event.detail.id);
    } else {
      newSelectedRows.add(event.detail.id);
    }

    selectedRows = newSelectedRows;
  };

  const toggleSelectAll = (event: MouseEvent) => {
    event.stopPropagation();
    const newSelectedRows = new Set<string>();

    if (!allSelected) {
      for (const settlement of loadedSettlementSummary) {
        newSelectedRows.add(settlement.id);
      }
    }

    selectedRows = newSelectedRows;
  };

  const handleOverlayAction = (event: CustomEvent<{ actionId: string }>) => {
    const { actionId } = event.detail;

    if (actionId === "delete") {
      dispatch("delete");
    }
  };
</script>

<div
  class="p-4 h-16 w-full sticky top-0 flex items-center bg-white dark:bg-neutral-900 z-10 border-b
   border-gray-200 dark:border-neutral-600"
>
  {#if selectedRows.size > 0}
    <div class="absolute inset-0 flex items-center justify-center ml-36 z-0">
      <div class="w-full">
        <SelectionOverlay
          selectedCount={selectedRows.size}
          {overlayActions}
          on:overlayAction={handleOverlayAction}
        />
      </div>
    </div>
  {/if}
  <ButtonNew
    id="settlements-select-all"
    variant="outline"
    size="small"
    color="white"
    class="absolute right-4 top-2"
    on:click={toggleSelectAll}
    ><div class="flex gap-4 p-1 h-full">
      <Checkbox
        id="settlements-select-all"
        checked={allSelected}
        on:change={() => {
          toggleSelectAll(new MouseEvent("click"));
        }}
      />
      Select All
    </div>
  </ButtonNew>
</div>

<div class="flex flex-col gap-4 p-4">
  {#each loadedSettlementSummary as settlement}
    <SettlementCard
      id={settlement.id}
      {settlement}
      on:checkboxToggle={toggleSelectRow}
      checked={selectedRows.has(settlement.id)}
    />
  {/each}
</div>
