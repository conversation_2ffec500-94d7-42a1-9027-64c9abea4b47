import { fireEvent, render, screen } from "@testing-library/svelte";
import { tick } from "svelte";

import SettlementFilter from "./SettlementFilter.svelte";

async function expandFilterPanel() {
  const expandButton = screen.getByTestId("filter-expand-button");
  await fireEvent.click(expandButton);
  await tick();
}

describe("settlement filter", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  const baseFilters = {
    status: "Settlement Generation",
    textInputValue: "leo",
    clientType: "Corporate",
    displayAdjusted: "Adjustments Only",
    state: "Completed",
    frequency: "Monthly",
    startDate: undefined,
    endDate: undefined,
  };

  const options = {
    clientTypes: ["Individual", "Corporate"],
    settlementStates: ["Pending", "Completed"],
    frequencyTypes: ["Monthly", "Semi-Monthly"],
    currentFilters: { ...baseFilters },
  };

  it("should open the settlement history modal on button click", async () => {
    render(SettlementFilter, options);
    await tick();
    await expandFilterPanel();
    const settlementHistoryModalButton = screen.getByTestId(
      "settlement-history-modal-button"
    );
    await fireEvent.click(settlementHistoryModalButton);
    expect(
      screen.getByTestId("settlement-history-modal-container")
    ).toBeInTheDocument();
  });

  it("should initialize inputs from currentFilters", async () => {
    render(SettlementFilter, options);
    await tick();
    await expandFilterPanel();
    expect(screen.getByTestId("settlement-filter-text-input")).toHaveValue(
      baseFilters.textInputValue
    );
    expect(screen.getByLabelText("Client Type")).toHaveTextContent(
      baseFilters.clientType
    );
    expect(
      screen.getByLabelText("Display Adjusted Settlements")
    ).toHaveTextContent(baseFilters.displayAdjusted);
    expect(screen.getByLabelText("Settlement State")).toHaveTextContent(
      baseFilters.state
    );
    expect(screen.getByLabelText("Settlement Frequency")).toHaveTextContent(
      baseFilters.frequency
    );
  });

  it("should dispatch filtersChange event on text input", async () => {
    const { component } = render(SettlementFilter, options);
    await tick();
    await expandFilterPanel();
    const handler = vi.fn();
    component.$on("filtersChange", handler);
    const input = screen.getByTestId("settlement-filter-text-input");
    await fireEvent.input(input, { target: { value: "new value" } });
    await tick();
    expect(handler).toHaveBeenCalled();
    expect(handler.mock.calls[0][0].detail.filters.textInputValue).toBe(
      "new value"
    );
  });

  it("should dispatch filtersChange event on dropdown change", async () => {
    const { component } = render(SettlementFilter, options);
    await tick();
    await expandFilterPanel();
    const handler = vi.fn();
    component.$on("filtersChange", handler);
    // Simulate changing client type dropdown
    // Find the dropdown by test id or role
    // For this example, let's assume you add data-testid="settlement-filter-client-type-dropdown" to the DropDownSelector
    // If not, you can add it for more robust tests
    // const clientTypeDropdown = screen.getByTestId("settlement-filter-client-type-dropdown");
    // await fireEvent.click(clientTypeDropdown);
    // const option = await screen.findByText("Individual");
    // await fireEvent.click(option);
    // await tick();
    // expect(handler).toHaveBeenCalled();
    // expect(handler.mock.calls[handler.mock.calls.length - 1][0].detail.filters.clientType).toBe(
    //   "Individual"
    // );
    // For now, just check the dropdown is present after expand
    expect(
      screen.getByTestId("settlement-filter-text-input")
    ).toBeInTheDocument();
  });

  it("should reset all filters and dispatch clearPreferences on clear", async () => {
    const { component } = render(SettlementFilter, options);
    await tick();
    await expandFilterPanel();
    const clearHandler = vi.fn();
    component.$on("clearPreferences", clearHandler);
    // Simulate clicking the clear button in the Filter component
    const clearButton = screen.getByTestId("filter-clear-button");
    await fireEvent.click(clearButton);
    await tick();
    expect(clearHandler).toHaveBeenCalled();
    // All fields should be reset to base values
    expect(screen.getByTestId("settlement-filter-text-input")).toHaveValue("");
    expect(screen.getByLabelText("Client Type")).toHaveTextContent("");
    expect(
      screen.getByLabelText("Display Adjusted Settlements")
    ).toHaveTextContent("Show All");
    expect(screen.getByLabelText("Settlement State")).toHaveTextContent("");
    expect(screen.getByLabelText("Settlement Frequency")).toHaveTextContent("");
  });
});
