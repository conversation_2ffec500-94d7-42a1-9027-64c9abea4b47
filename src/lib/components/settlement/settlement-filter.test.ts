import { fireEvent, render, screen } from "@testing-library/svelte";
import { tick } from "svelte";

import SettlementFilter from "./SettlementFilter.svelte";

describe("settlement filter", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("should open the settlement history modal on button click", async () => {
    const options = {
      clientTypes: ["Individual", "Corporate"],
      settlementStates: ["Pending", "Completed"],
      frequencyTypes: ["Monthly", "Semi-Monthly"],
      filters: {
        status: "Settlement Generation",
        textInputValue: "",
        clientType: "",
        displayAdjusted: "Show All",
        state: "",
        frequency: "",
        startDate: undefined,
        endDate: undefined,
      },
    };

    render(SettlementFilter, options);

    await tick();

    const settlementHistoryModalButton = screen.getByTestId(
      "settlement-history-modal-button"
    );
    await fireEvent.click(settlementHistoryModalButton);

    expect(
      screen.getByTestId("settlement-history-modal-container")
    ).toBeInTheDocument();
  });
});
