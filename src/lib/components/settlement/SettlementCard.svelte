<script lang="ts">
  import { goto } from "$app/navigation";
  import BalancesCardItem from "$lib/components/settlement/card-items/BalancesCardItem.svelte";
  import CustomerCardItem from "$lib/components/settlement/card-items/CustomerCardItem.svelte";
  import DatesCardItem from "$lib/components/settlement/card-items/DatesCardItem.svelte";
  import ErrorCardItem from "$lib/components/settlement/card-items/ErrorCardItem.svelte";
  import PlatformsCardItem from "$lib/components/settlement/card-items/PlatformsCardItem.svelte";
  import StatusCardItem from "$lib/components/settlement/card-items/StatusCardItem.svelte";
  import Button from "$lib/components/ui/buttons/ButtonNew.svelte";
  import Card from "$lib/components/ui/card/Card.svelte";

  import type { SettlementSummary } from "$lib/types/settlement/types";

  export let id: string;
  export let settlement: SettlementSummary;
  export let checked: boolean;
  export let hasCheckbox = true;

  function navigateToSummary() {
    const sanitizedCustomerId = encodeURIComponent(settlement.id);
    goto(`/settlement/${sanitizedCustomerId}/summary`).catch((error) => {
      console.error("Navigation error:", error);
    });
  }
</script>

<Card
  on:checkboxToggle
  {checked}
  {id}
  {hasCheckbox}
  leadingItem={{
    component: CustomerCardItem,
    props: {
      customerName: settlement.customerName,
      serviceNumber: settlement.serviceNumber,
      customerType: settlement.customerType,
    },
  }}
  items={[
    { component: StatusCardItem, props: { status: settlement.status } },
    {
      component: BalancesCardItem,
      props: {
        netPayout: settlement.netPayout,
        endBalance: settlement.endBalance,
      },
      hasDivider: true,
    },
    {
      component: DatesCardItem,
      props: {
        fromDate: settlement.fromDate,
        toDate: settlement.toDate,
      },
      hasDivider: true,
    },
    {
      component: PlatformsCardItem,
      props: { platformSettlements: settlement.platformSettlements },
      hasDivider: true,
    },
    settlement.error
      ? {
          component: ErrorCardItem,
          props: {
            error: settlement.error,
          },
          hasDivider: true,
        }
      : undefined,
  ]}
>
  <div slot="actions" class="flex items-center gap-4">
    {#if hasCheckbox}
      <Button
        intent="outline"
        color="dark"
        className="dark:bg-neutral-700 rounded-full w-12 h-12"
      >
        <i class="bi bi-folder" />
      </Button>
      <Button
        intent="outline"
        color="dark"
        className="dark:bg-neutral-700 rounded-full w-12 h-12"
      >
        <i class="bi bi-envelope" />
      </Button>
      <Button
        on:click={navigateToSummary}
        intent="outline"
        color="dark"
        className="dark:bg-neutral-700 rounded-full w-12 h-12"
      >
        <i class="bi bi-chevron-right" />
      </Button>
    {/if}
  </div>
</Card>
