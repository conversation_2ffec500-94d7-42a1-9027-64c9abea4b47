<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import Modal from "$lib/components/ui/modals/Modal.svelte";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
  import Table from "$lib/components/ui/table/Table.svelte";

  export let selectedSettlementIds: Set<string>;
  export let failedToDeleteSettlements: Array<{
    settlementId: string;
    customerName: string;
    serviceNumber: string;
    fromDate: string;
    toDate: string;
  }> = [];
  export let showModal: boolean;
  export let isDeleting: boolean;
  export let details: {
    customerName: string;
    fromDate: string;
    toDate: string;
  } = {
    customerName: "",
    fromDate: "",
    toDate: "",
  };

  const dispatch = createEventDispatcher();

  const handleConfirm = () => {
    // Confirm will just close the modal if selected settlements all have approved wires.
    if (selectedSettlementIds.size === failedToDeleteSettlements.length) {
      showModal = false;

      return;
    }

    dispatch("confirm");
  };
</script>

<Modal
  id="delete-settlements-modal"
  title="Delete Settlements"
  bind:showModal
  on:confirm={handleConfirm}
  closeOnOutsideClick={false}
>
  <div slot="content">
    {#if isDeleting}
      <div class="flex h-24 w-full items-center justify-center">
        <LoadingSpinner extraClass="w-12 h-12" />
      </div>
    {:else if selectedSettlementIds.size === 1 && failedToDeleteSettlements.length === 1}
      <p>
        The wire related to this settlement is approved. Please unapprove before
        deleting.
      </p>
    {:else if failedToDeleteSettlements.length > 0}
      <p class="mb-4">
        {#if selectedSettlementIds.size === failedToDeleteSettlements.length}
          The selected settlements cannot be deleted due to approved wires.
          Please unapprove before deleting.
        {:else}
          The following settlements below cannot be deleted due to approved
          wires. Would you like to continue deleting the other settlements?
        {/if}
      </p>
      <Table
        headers={[
          { id: "client", title: "Client", align: "left" },
          { id: "fromDateToDate", title: "From - To Date", align: "left" },
        ]}
        rows={failedToDeleteSettlements.map((settlement) => ({
          id: settlement.settlementId,
          cells: [
            {
              component: ListText,
              props: {
                lines: [
                  [
                    {
                      text: `${settlement.customerName}`,
                      classes: "font-semibold",
                    },
                  ],
                  [
                    {
                      text: `${settlement.serviceNumber}`,
                      classes: "text-gray-400",
                    },
                  ],
                ],
              },
            },
            {
              component: ListText,
              props: {
                lines: [
                  [
                    {
                      text: "From Date",
                      classes: "text-gray-500 text-sm mb-1",
                    },
                  ],
                  [
                    {
                      text: `${settlement.fromDate}`,
                      classes: "font-medium mb-2",
                    },
                  ],
                  [
                    {
                      text: "To Date",
                      classes: "text-gray-500 text-sm mb-1",
                    },
                  ],
                  [
                    {
                      text: `${settlement.toDate}`,
                      classes: "font-medium",
                    },
                  ],
                ],
              },
            },
          ],
        }))}
      />
    {:else if details.customerName && details.fromDate && details.toDate}
      <p>
        Are you sure you want to delete {details.customerName} settlement for {details.fromDate}
        - {details.toDate}?
      </p>
    {:else}
      <p>
        Are you sure you want to delete {selectedSettlementIds.size}
        {selectedSettlementIds.size > 1 ? " settlements?" : " settlement?"}
      </p>
    {/if}
  </div>
</Modal>
