<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import { getAvailableDays } from "$lib/components/ui/date-pickers/custom/custom-date-frequency";
  import CustomRangePicker from "$lib/components/ui/date-pickers/custom/CustomRangePicker.svelte";
  import DropDownSelector from "$lib/components/ui/drop-down/DropDownSelector.svelte";
  import Modal from "$lib/components/ui/modals/Modal.svelte";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import { request } from "$lib/services/api-caller/request";

  const dispatch = createEventDispatcher();

  type Frequency =
    | "Monthly"
    | "Semi-Monthly"
    | "Twice Per Week"
    | "Weekly-Friday"
    | "Weekly-Monday"
    | "";
  let allFrequencies: Array<{ value: string; label: string; id: number }> = [];
  let frequencyId: number | undefined;

  export let showModal: boolean;
  export let frequency: Frequency | "" = "";

  let isLoading = false;
  let fromDate: Date;
  let toDate: Date;
  let hasExistingSettlement = false;
  const lastGeneratedData: Record<
    Frequency,
    {
      userName: string;
      createdAt: string;
    }
  > = {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    "": { userName: "", createdAt: "" },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    Monthly: { userName: "", createdAt: "" },
    "Semi-Monthly": { userName: "", createdAt: "" },
    "Twice Per Week": {
      userName: "",
      createdAt: "",
    },
    "Weekly-Friday": {
      userName: "",
      createdAt: "",
    },
    "Weekly-Monday": {
      userName: "",
      createdAt: "",
    },
  };
  export let error: string | "" = "";

  $: if (showModal) {
    // eslint-disable-next-line unicorn/prefer-top-level-await
    void loadHistory();
  }
  async function loadHistory() {
    try {
      isLoading = true;
      error = "";

      const response = await request.get<{
        frequencyJobs: Record<
          Frequency,
          {
            jobId: number;
            fromDate: string;
            toDate: string;
            status: string;
            userId: number;
            userName: string;
            frequencyId: string;
            frequencyName: string;
            createdAt: string;
          }
        >;
        allFrequencies: Array<{
          frequencyId: number;
          frequencyCode: string;
          frequencyName: string;
        }>;
      }>("/settlement/all/frequency/read");

      processFrequencies(response.allFrequencies);
      processFrequencyJobs(response.frequencyJobs);
    } catch {
      error = "Error. Could not get last generated date or user.";
    } finally {
      isLoading = false;
    }
  }

  function processFrequencies(
    backendFrequencies: Array<{ frequencyName: string; frequencyId: number }>
  ) {
    allFrequencies = backendFrequencies.map((freq) => ({
      value: freq.frequencyName,
      label: freq.frequencyName,
      id: freq.frequencyId,
    }));
  }

  type FrequencyJob = {
    userName: string;
    createdAt: string;
  };

  const processFrequencyJobs = (
    frequencyJobs: Record<Frequency, FrequencyJob>
  ) => {
    for (const freq of allFrequencies) {
      const frequencyName = freq.value as Frequency;
      const data = frequencyJobs[frequencyName];

      lastGeneratedData[frequencyName] = data
        ? { userName: data.userName, createdAt: data.createdAt }
        : { userName: "-", createdAt: "-" };
    }
  };

  function formatDateTime(dateString: string): string {
    if (!dateString) return "-";
    const date = new Date(dateString);

    return date.toLocaleString();
  }

  function handleConfirm() {
    if (
      hasExistingSettlement ||
      !frequency ||
      !frequencyId ||
      !fromDate ||
      !toDate
    ) {
      return;
    }

    dispatch("confirm", { frequency, frequencyId, fromDate, toDate });
  }

  function handleDateChange(
    event: CustomEvent<{ startDate?: Date; endDate?: Date }>
  ) {
    const { startDate, endDate } = event.detail;

    fromDate = startDate ?? new Date();
    toDate = endDate ?? new Date();
  }

  function formatDateToYyyyMmDd(dateString: Date) {
    const date = new Date(dateString);
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  }

  function handleFrequencyChange(event: CustomEvent<{ value: string }>) {
    frequency = event.detail.value as Frequency;

    const foundFrequencyId = allFrequencies.find(
      (freq) => freq.value === frequency
    )?.id;

    hasExistingSettlement = false;
    frequencyId = foundFrequencyId;

    const today = new Date();
    const { start, end } = getAvailableDays(today, frequency);

    fromDate = start;
    toDate = end;
  }
</script>

<Modal
  title="Generate Settlement"
  id="generate-settlement-confirm"
  closeOnOutsideClick={true}
  bind:showModal
  on:confirm={handleConfirm}
>
  <div
    slot="content"
    class="flex flex-col h-80 justify-between text-gray-800 dark:text-white"
  >
    <div class="flex flex-col gap-4">
      {#if isLoading}
        <div
          class="flex justify-center items-center"
          data-testid="loading-spinner"
        >
          <LoadingSpinner />
        </div>
      {:else}
        <DropDownSelector
          id="generate-settlement-modal-dropdown"
          label="Statement Frequency"
          labelText={frequency}
          bind:value={frequency}
          items={allFrequencies}
          placeholder="Select Frequency"
          on:change={handleFrequencyChange}
        />

        {#if frequency}
          <div data-testid="custom-range-picker">
            <CustomRangePicker
              {frequency}
              id="generate-settlement-date-range"
              startDate={fromDate}
              endDate={toDate}
              on:change={handleDateChange}
            />
          </div>
        {/if}
      {/if}

      <p>From Date: {(fromDate && formatDateToYyyyMmDd(fromDate)) || "---"}</p>
      <p>To Date: {(fromDate && formatDateToYyyyMmDd(toDate)) || "---"}</p>
    </div>
    <div class="flex flex-col items-end gap-2">
      {#if error}
        <p class="text-red-500">{error}</p>
      {:else}
        <p>
          Last Generated By: {lastGeneratedData[frequency].userName || "-"}
        </p>
        <p>
          Last Generated At:
          {frequency && lastGeneratedData[frequency].createdAt
            ? formatDateTime(lastGeneratedData[frequency].createdAt)
            : "-"}
        </p>
      {/if}
    </div>
  </div>
</Modal>
