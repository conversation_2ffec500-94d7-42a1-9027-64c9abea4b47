import { render, waitFor } from "@testing-library/svelte";
import { describe, it, expect, vi } from "vitest";

import GenerateSettlementModal from "./GenerateSettlementsModal.svelte";

vi.mock("$lib/services/api-caller/request", () => ({
  request: {
    get: vi.fn().mockResolvedValue({
      allFrequencies: [
        { frequencyId: 1, frequencyCode: "M", frequencyName: "Monthly" },
        { frequencyId: 2, frequencyCode: "SM", frequencyName: "Semi-Monthly" },
      ],
      frequencyJobs: {
        "Semi-Monthly": {
          userName: "Test User",
          createdAt: "2023-01-01T00:00:00Z",
        },
      },
    }),
  },
}));

describe("GenerateSettlementModal", () => {
  it("should not render CustomRangePicker when frequency is empty", async () => {
    const { queryByTestId } = render(GenerateSettlementModal, {
      props: { showModal: true, frequency: "" },
    });

    await waitFor(() => {
      expect(queryByTestId("custom-range-picker")).not.toBeInTheDocument();
    });
  });

  it("should render CustomRangePicker when frequency is set", async () => {
    const { getByTestId, queryByTestId } = render(GenerateSettlementModal, {
      props: { showModal: true, frequency: "Monthly" },
    });

    await waitFor(() => {
      expect(queryByTestId("loading-spinner")).not.toBeInTheDocument();
    });

    await waitFor(() => {
      expect(getByTestId("custom-range-picker")).toBeInTheDocument();
    });
  });
});
