<script lang="ts">
  import DropDownSelector from "$lib/components/ui/drop-down/DropDownSelector.svelte";
  import Modal from "$lib/components/ui/modals/Modal.svelte";

  export let showModal: boolean;

  const displayOnExcelOptions = ["True", "False"];

  let selectedDisplayOnExcel = displayOnExcelOptions[0];

  function handleDisplayOnExcelChange(event: CustomEvent<string>) {
    selectedDisplayOnExcel = event.detail;
    console.log("Selected Display on Excel:", selectedDisplayOnExcel);
  }
</script>

<Modal
  title="Add Adjustment"
  subtitle="Calculate all settlements details"
  id="add-adjustment-modal"
  closeOnOutsideClick={true}
  bind:showModal
>
  <div slot="content" class="py-2">
    <div class="flex flex-col items-start gap-4">
      <div class="w-full">
        <span class="font-medium text-gray-900 dark:text-white">Label</span>
        <div
          class="w-full mt-2 border dark:border-neutral-600
          rounded-md p-3 text-gray-900 dark:text-white"
        >
          -
        </div>
      </div>
      <div class="w-full">
        <span class="font-medium text-gray-900 dark:text-white">Amount</span>
        <div
          class="mt-2 border dark:border-neutral-600
          rounded-md w-full p-3 text-gray-900 dark:text-white"
        >
          -
        </div>
      </div>
      <div class="w-full">
        <span class="font-medium text-gray-900 dark:text-white"
          >Display on Excel</span
        >
        <div class="w-full mt-2">
          <DropDownSelector
            id="email-filters-status"
            items={displayOnExcelOptions}
            bind:value={selectedDisplayOnExcel}
            on:change={handleDisplayOnExcelChange}
            clearable={false}
            classOverride="
          flex w-full rounded-md border dark:border-neutral-600 shadow-sm bg-white
          cursor-pointer text-gray-900 dark:text-white
        "
          />
        </div>
      </div>
      <div class="w-full">
        <span
          class="font-medium text-gray-900
         dark:text-white">Comment</span
        >
        <div
          class="mt-2 border dark:border-neutral-600
          rounded-md w-full p-3 text-gray-900 dark:text-white"
        >
          -
        </div>
      </div>
    </div>
  </div>
</Modal>
