<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import DropDownSelector from "$lib/components/ui/drop-down/DropDownSelector.svelte";
  import TextInput from "$lib/components/ui/inputs/TextInput.svelte";
  import Modal from "$lib/components/ui/modals/Modal.svelte";

  export let showModal: boolean;

  const displayOnExcelOptions = ["True", "False"];

  let selectedDisplayOnExcel = displayOnExcelOptions[0];

  const dispatch = createEventDispatcher();

  let label = "";
  let amount = "";
  let comment = "";

  function handleConfirm() {
    dispatch("confirm", {
      label,
      amount,
      displayOnExcel: selectedDisplayOnExcel,
      comment,
    });

    showModal = false;

    clearValues();
  }

  function clearValues() {
    label = "";
    amount = "";
    comment = "";
    selectedDisplayOnExcel = displayOnExcelOptions[0];
  }
</script>

<Modal
  title="Add Adjustment"
  subtitle="Calculate all settlements details"
  id="add-adjustment-modal"
  closeOnOutsideClick={true}
  bind:showModal
  on:confirm={() => {
    handleConfirm();
  }}
>
  <div slot="content" class="py-2 flex flex-col items-start gap-4 w-full">
    <TextInput label={"Label"} placeholder={"-"} bind:value={label} />
    <TextInput
      label={"Amount"}
      placeholder={"-"}
      type="number"
      bind:value={amount}
    />
    <div class="w-full">
      <DropDownSelector
        id="adjustments-display-on-excel"
        items={displayOnExcelOptions}
        bind:value={selectedDisplayOnExcel}
        label={"Display on Excel"}
        clearable={false}
        classOverride="
          flex w-full rounded-md border dark:border-neutral-600 shadow-sm bg-white
          cursor-pointer text-gray-900 dark:text-white
        "
      />
    </div>
    <TextInput label={"Comment"} placeholder={"-"} bind:value={comment} />
  </div>
</Modal>
