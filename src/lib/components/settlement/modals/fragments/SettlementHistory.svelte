<script lang="ts">
  import { onMount } from "svelte";

  import Table from "../../../ui/table/Table.svelte";
  import {
    getTableHeaders,
    getTableRows,
  } from "../../functions/get-settlement-history-data";

  import { failureToast } from "$lib/components/ui/notifications/toast";
  import PaginationFooter from "$lib/components/ui/pagination/PaginationFooter.svelte";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import { type Row } from "$lib/components/ui/table/types";
  import { request } from "$lib/services/api-caller/request";
  import { type RequestError } from "$lib/utils/errors/request-error";

  import type { SettlementHistory } from "$lib/types/settlement/types";

  const headers = getTableHeaders();
  let data: SettlementHistory[] = [];
  let tableRows: Row[] = [];
  let isLoading = false;

  let currentPage = 1;
  let recordsPerPage = 20;
  let totalCount = 0;

  $: tableRows = getTableRows(data);

  $: lastPage = Math.ceil(totalCount / recordsPerPage);

  onMount(async () => {
    await loadHistory();
  });

  async function loadHistory() {
    try {
      const offset = (currentPage - 1) * recordsPerPage;
      isLoading = true;

      const queryParameters = new URLSearchParams({
        offset: offset.toString(),
        limit: recordsPerPage.toString(),
      });

      const response = await request.get<{
        jobs: SettlementHistory[];
        totalCount: number;
      }>(`/settlement/all/history?${queryParameters.toString()}`);

      data = response?.jobs ?? [];
      totalCount = response?.totalCount ?? 0;
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError.message);
    } finally {
      isLoading = false;
    }
  }

  async function handlePageChange(event: CustomEvent<{ pageNumber: number }>) {
    currentPage = event.detail.pageNumber;
    await loadHistory();
  }

  async function handleRecordsPerPageChange(
    event: CustomEvent<{ records: number }>
  ) {
    recordsPerPage = event.detail.records;
    currentPage = 1;
    await loadHistory();
  }
</script>

<div class="p-2">
  {#if isLoading}
    <div class="flex-1 flex w-full h-full items-center justify-center">
      <LoadingSpinner extraClass="w-12 h-12" />
    </div>
  {:else}
    <Table {headers} rows={tableRows}></Table>
  {/if}
  <PaginationFooter
    id="settlement-history-footer"
    {lastPage}
    {currentPage}
    totalNumberOfItemsListed={totalCount}
    limit={String(recordsPerPage)}
    on:pageChange={handlePageChange}
    on:recordsPerPageChange={handleRecordsPerPageChange}
  />
</div>
