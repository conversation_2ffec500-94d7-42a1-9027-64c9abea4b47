import { getSortKeyAndSortOrder } from "./get-sort-key-and-sort-order";

describe("getSortKeyAndSortOrder", () => {
  it('should return the correct sort key and order for "From Date: Old - New"', () => {
    const result = getSortKeyAndSortOrder("From Date: Old - New");
    expect(result).toEqual({ sortKey: "fromDate", sortOrder: "asc" });
  });

  it('should return the correct sort key and order for "From Date: New - Old"', () => {
    const result = getSortKeyAndSortOrder("From Date: New - Old");
    expect(result).toEqual({ sortKey: "fromDate", sortOrder: "desc" });
  });

  it('should return the correct sort key and order for "To Date: Old - New"', () => {
    const result = getSortKeyAndSortOrder("To Date: Old - New");
    expect(result).toEqual({ sortKey: "toDate", sortOrder: "asc" });
  });

  it('should return the correct sort key and order for "To Date: New - Old"', () => {
    const result = getSortKeyAndSortOrder("To Date: New - Old");
    expect(result).toEqual({ sortKey: "toDate", sortOrder: "desc" });
  });

  it('should return the correct sort key and order for "Client Name: A-Z"', () => {
    const result = getSortKeyAndSortOrder("Client Name: A-Z");
    expect(result).toEqual({ sortKey: "clientName", sortOrder: "asc" });
  });

  it('should return the correct sort key and order for "Client Name: Z-A"', () => {
    const result = getSortKeyAndSortOrder("Client Name: Z-A");
    expect(result).toEqual({ sortKey: "clientName", sortOrder: "desc" });
  });

  it("should return the default sort key and order for an unknown value", () => {
    const result = getSortKeyAndSortOrder("Unknown Value");
    expect(result).toEqual({ sortKey: "fromDate", sortOrder: "asc" });
  });
});
