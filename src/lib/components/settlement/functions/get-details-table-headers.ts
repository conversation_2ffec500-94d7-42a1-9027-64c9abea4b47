import type { Head<PERSON> } from "$lib/components/ui/table/types";
import type { PlatformCode } from "$lib/types/settlement/types";

const summaryHeaders: Header[] = [
  { id: "information", title: "Information", align: "left", sortable: false },
  {
    id: "numOfTransactions",
    title: "# of Transactions",
    align: "left",
    sortable: false,
  },
  {
    id: "transactionValue",
    title: "Transaction Value",
    align: "left",
    sortable: false,
  },
  {
    id: "accountPayableReceivable",
    title: "Account Payable / (Receivable)",
    align: "left",
    sortable: false,
  },
];

const platformHeaders: Header[] = [
  { id: "information", title: "Information", align: "left", sortable: false },
  { id: "count", title: "Count", align: "left", sortable: false },
  { id: "value", title: "Value", align: "left", sortable: false },
];

export const getTableHeaders = (platform: PlatformCode): Header[] => {
  if (platform === "SUMMARY") {
    return summaryHeaders;
  }

  return platformHeaders;
};
