import { request } from "$lib/services/api-caller/request";

export async function getSettlementFilterOptions(): Promise<{
  clientTypes: string[];
  settlementStates: string[];
  settlementFrequencies: string[];
}> {
  try {
    const response = await request.get<{
      clientTypes: string[];
      settlementStates: string[];
      settlementFrequencies: string[];
    }>("/settlement/filters");

    return response;
  } catch (error) {
    console.error("Error fetching settlement data:", (error as Error).message);
    throw error;
  }
}
