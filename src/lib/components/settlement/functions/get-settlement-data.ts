import { request } from "$lib/services/api-caller/request";
import { formatDateStringToYYYYMMDD } from "$lib/utils/date-utils";

import type {
  GroupedSettlement,
  SettlementFilters,
} from "$lib/types/settlement/types";

type DataRequest = {
  pageNumber: number;
  recordsPerPage: number;
  sortKey: string;
  sortOrder: string;
  filters: SettlementFilters;
};

export async function fetchData({
  pageNumber,
  recordsPerPage,
  sortKey,
  sortOrder,
  filters,
}: DataRequest): Promise<{
  totalCount: number;
  settlements: GroupedSettlement[];
}> {
  const offset = (pageNumber - 1) * recordsPerPage;

  const { textInputValue } = filters;
  const { clientType } = filters;
  const { displayAdjusted } = filters;
  const { state } = filters;
  const { frequency } = filters;
  const { startDate } = filters;
  const { endDate } = filters;
  const { status } = filters;

  try {
    const { newStartDate, newEndDate } = removeTimeFromDates(
      startDate,
      endDate
    );
    const response = await request.post<{
      totalCount: number;
      settlements: GroupedSettlement[];
    }>("/settlement", {
      offset,
      limit: recordsPerPage,
      sortKey,
      status,
      sortOrder,
      nameOrServiceNumber: textInputValue,
      clientType,
      displayAdjusted,
      state,
      frequency,
      startDate: newStartDate,
      endDate: newEndDate,
    });

    return response;
  } catch (error) {
    console.error("Error fetching settlement data:", (error as Error).message);
    throw error;
  }
}

function removeTimeFromDates(
  startDate: Date | undefined,
  endDate: Date | undefined
): { newStartDate: string; newEndDate: string } {
  const newStartDate = startDate ? formatDateStringToYYYYMMDD(startDate) : "";
  const newEndDate = endDate ? formatDateStringToYYYYMMDD(endDate) : "";

  return { newStartDate, newEndDate };
}
