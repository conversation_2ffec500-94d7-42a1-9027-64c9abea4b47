import { request } from "$lib/services/api-caller/request";
import { formatDateStringToYYYYMMDD } from "$lib/utils/date-utils";

import type {
  GroupedSettlement,
  SettlementFilters,
  SettlementPreference,
} from "$lib/types/settlement/types";

type DataRequest = {
  pageNumber: number;
  recordsPerPage: number;
  sortKey: string;
  sortOrder: string;
  filters: SettlementFilters;
};

export const fetchData = async ({
  pageNumber,
  recordsPerPage,
  sortKey,
  sortOrder,
  filters,
}: DataRequest): Promise<{
  totalCount: number;
  settlements: GroupedSettlement[];
}> => {
  const offset = (pageNumber - 1) * recordsPerPage;

  const {
    textInputValue,
    clientType,
    displayAdjusted,
    state,
    frequency,
    startDate,
    endDate,
    status,
  } = filters;

  try {
    const { newStartDate, newEndDate } = removeTimeFromDates(
      startDate,
      endDate
    );
    const response = await request.post<{
      totalCount: number;
      settlements: GroupedSettlement[];
    }>("/settlement", {
      offset,
      limit: recordsPerPage,
      sortKey,
      status,
      sortOrder,
      nameOrServiceNumber: textInputValue,
      clientType,
      displayAdjusted,
      state,
      frequency,
      startDate: newStartDate,
      endDate: newEndDate,
    });

    return response;
  } catch (error) {
    console.error("Error fetching settlement data:", (error as Error).message);
    throw error;
  }
};

export const getSettlementPreference =
  async (): Promise<SettlementPreference> => {
    try {
      const response = await request.get<SettlementPreference>(
        "/settlement/preference"
      );

      return response;
    } catch (error) {
      console.error(
        "Error fetching settlement preference:",
        (error as Error).message
      );
      throw error;
    }
  };

export const saveSettlementPreference = async (
  preference: SettlementPreference
): Promise<void> => {
  try {
    await request.put("/settlement/preference", preference);
  } catch (error) {
    console.error(
      "Error saving settlement preference:",
      (error as Error).message
    );
    throw error;
  }
};

const removeTimeFromDates = (
  startDate: Date | undefined,
  endDate: Date | undefined
): { newStartDate: string; newEndDate: string } => {
  const newStartDate = startDate ? formatDateStringToYYYYMMDD(startDate) : "";
  const newEndDate = endDate ? formatDateStringToYYYYMMDD(endDate) : "";

  return { newStartDate, newEndDate };
};
