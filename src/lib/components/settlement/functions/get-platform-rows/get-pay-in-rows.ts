import { constructRow } from "./construct-row";

import { minimumFeeGroup } from "$lib/constants/platforms";

import type { Row } from "$lib/components/ui/table/types";
import type {
  Platform,
  PlatformCode,
  SettlementDetails,
} from "$lib/types/settlement/types";

export const getPayInRows = (
  platformCode: PlatformCode,
  settlementDetails: SettlementDetails
): Row[] => {
  const rows: Row[] = [];
  const {
    transactionCount,
    totalTransactionAmount,
    minimumFeeCount,
    totalMinimumAmount,
    refundCount,
    gatewayFee,
    transactionFee,
    salesFee,
    minimumFeeTotal,
    refundFee,
    totalRefundAmount,
  } = settlementDetails;

  rows.push(
    constructRow(
      "Transaction",
      transactionCount - minimumFeeCount,
      totalTransactionAmount - totalMinimumAmount
    ),
    ...(minimumFeeGroup.includes(platformCode as Platform)
      ? [
          constructRow(
            "Minimum Fee Transactions",
            minimumFeeCount,
            totalMinimumAmount
          ),
        ]
      : []),
    constructRow("Total Payouts", undefined, totalTransactionAmount),
    constructRow("Refund Count", refundCount),
    constructRow("Gateway Fee", undefined, gatewayFee),
    constructRow("Transaction Fee", undefined, transactionFee),
    constructRow("Sales Fee", undefined, salesFee),
    constructRow("Minimum Fee", undefined, minimumFeeTotal),
    constructRow("Refund Fee", undefined, refundFee),
    constructRow("Total Refund Amount", undefined, totalRefundAmount)
  );

  return rows;
};
