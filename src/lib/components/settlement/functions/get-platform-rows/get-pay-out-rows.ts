import { constructRow } from "./construct-row";

import { partialReturnGroup, rejected1Group } from "$lib/constants/platforms";

import type { Row } from "$lib/components/ui/table/types";
import type {
  Platform,
  PlatformCode,
  SettlementDetails,
} from "$lib/types/settlement/types";

export const getPayOutRows = (
  platformCode: PlatformCode,
  settlementDetails: SettlementDetails
): Row[] => {
  const rows: Row[] = [];
  const {
    transactionCount,
    totalTransactionAmount,
    totalFailedAmount,
    txnCountETI_R1: rejected1Count,
    partialReturnCountRTO: partialReturnCount,
    refundCount,
    gatewayFee,
    transactionFee,
    salesFee,
    minimumFeeTotal,
    refundFee,
    totalRefundAmount,
  } = settlementDetails;

  rows.push(
    constructRow("Transaction", transactionCount, totalTransactionAmount),
    constructRow("Transaction Failure", undefined, totalFailedAmount),
    ...(rejected1Group.includes(platformCode as Platform)
      ? [constructRow("Transactions Rejection (ETI Reject1)", rejected1Count)]
      : []),
    ...(partialReturnGroup.includes(platformCode as Platform)
      ? [constructRow("Partial refund Transactions", partialReturnCount)]
      : []),
    constructRow(
      "Total Payouts",
      undefined,
      totalTransactionAmount - totalFailedAmount
    ),
    constructRow("Refund Count", refundCount),
    constructRow("Gateway Fee", undefined, gatewayFee),
    constructRow("Transaction Fee", undefined, transactionFee),
    constructRow("Sales Fee", undefined, salesFee),
    constructRow("Minimum Fee", undefined, minimumFeeTotal),
    constructRow("Refund Fee", undefined, refundFee),
    constructRow("Total Refund Amount", undefined, totalRefundAmount)
  );

  return rows;
};
