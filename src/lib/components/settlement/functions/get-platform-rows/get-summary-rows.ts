import { constructRow } from "./construct-row";

import { payInGroup } from "$lib/constants/platforms";
import {
  type PlatformCode,
  type SettlementSummary,
} from "$lib/types/settlement/types";

import type { Row } from "$lib/components/ui/table/types";

export const getSummaryRows = (settlement: SettlementSummary): Row[] => {
  const rows: Row[] = [];

  const { customerType, platformSettlements } = settlement;

  for (const platform of Object.keys(platformSettlements) as PlatformCode[]) {
    if (platform !== "SUMMARY") {
      const {
        labelName,
        transactionCount,
        totalTransactionAmount,
        totalFailedAmount,
        totalPayable,
      } = platformSettlements[platform]!;

      if (customerType === "Merchant") {
        // For merchants, display totalPayable as a positive value for pay-in platforms,
        // and a negative value for pay-out platforms and KYC.
        if (payInGroup.includes(platform)) {
          rows.push(
            constructRow(
              labelName,
              transactionCount,
              totalTransactionAmount,
              totalPayable
            )
          );
        } else {
          rows.push(
            constructRow(
              labelName,
              transactionCount,
              totalTransactionAmount - totalFailedAmount,
              totalPayable === 0 ? totalPayable : -totalPayable
            )
          );
        }
      } else {
        // For non-merchants, display totalPayable as a positive value for all platforms.
        rows.push(
          constructRow(
            labelName,
            transactionCount,
            totalTransactionAmount,
            totalPayable
          )
        );
      }
    }
  }

  return rows;
};
