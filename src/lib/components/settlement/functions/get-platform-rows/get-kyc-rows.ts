import { constructRow } from "./construct-row";

import type { Row } from "$lib/components/ui/table/types";
import type { SettlementDetails } from "$lib/types/settlement/types";

export const getKycRows = (settlementDetails: SettlementDetails): Row[] => {
  const rows: Row[] = [];

  const { kycDetails } = settlementDetails;

  if (kycDetails) {
    for (const [
      kycType,
      { transactionCount, totalTransactionAmount },
    ] of Object.entries(kycDetails)) {
      rows.push(
        constructRow(kycType, transactionCount, totalTransactionAmount)
      );
    }
  }

  return rows;
};
