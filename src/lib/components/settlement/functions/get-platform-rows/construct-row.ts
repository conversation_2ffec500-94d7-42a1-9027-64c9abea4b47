import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
import { formatCurrency } from "$lib/utils/format-currency";

import type { Row } from "$lib/components/ui/table/types";

export const constructRow = (
  label: string,
  count?: number,
  value?: number,
  payable?: number
): Row => {
  const row: Row = {
    id: label,
    cells: [
      {
        component: ListText,
        props: {
          lines: [[{ text: label, classes: "dark:text-white" }]],
        },
      },
      {
        component: ListText,
        props: {
          lines: [
            [
              {
                text: count === undefined ? "" : count.toLocaleString(),
                classes: "dark:text-white",
              },
            ],
          ],
        },
      },
      {
        component: ListText,
        props: {
          lines: [
            [
              {
                text: value === undefined ? "" : formatCurrency(value),
                classes: "dark:text-white",
              },
            ],
          ],
        },
      },
    ],
  };

  if (payable !== undefined) {
    row.cells.push({
      component: ListText,
      props: {
        lines: [
          [
            {
              text: formatCurrency(payable),
              classes: "dark:text-white",
            },
          ],
        ],
      },
    });
  }

  return row;
};
