export function getSortKeyAndSortOrder(sortValue: string): {
  sortKey: string;
  sortOrder: string;
} {
  if (sortValue === "From Date: Old - New") {
    return { sortKey: "fromDate", sortOrder: "asc" };
  }

  if (sortValue === "From Date: New - Old") {
    return { sortKey: "fromDate", sortOrder: "desc" };
  }

  if (sortValue === "To Date: Old - New") {
    return { sortKey: "toDate", sortOrder: "asc" };
  }

  if (sortValue === "To Date: New - Old") {
    return { sortKey: "toDate", sortOrder: "desc" };
  }

  if (sortValue === "Client Name: A-Z") {
    return { sortKey: "clientName", sortOrder: "asc" };
  }

  if (sortValue === "Client Name: Z-A") {
    return { sortKey: "clientName", sortOrder: "desc" };
  }

  // If no value matches, return default values
  return { sortKey: "fromDate", sortOrder: "asc" };
}
