import { getKycRows } from "./get-platform-rows/get-kyc-rows";
import { getPayInRows } from "./get-platform-rows/get-pay-in-rows";
import { getPayOutRows } from "./get-platform-rows/get-pay-out-rows";
import { getSummaryRows } from "./get-platform-rows/get-summary-rows";

import { kycGroup, payInGroup, payOutGroup } from "$lib/constants/platforms";
import {
  type Platform,
  type PlatformCode,
  type SettlementSummary,
} from "$lib/types/settlement/types";

import type { Row } from "$lib/components/ui/table/types";

export const getTableRows = (
  selectedPlatform: PlatformCode,
  settlement: SettlementSummary
): Row[] => {
  let rows: Row[] = [];

  // Assume that if a platform is able to be selected, then its details are available
  if (payInGroup.includes(selectedPlatform as Platform)) {
    rows = getPayInRows(
      selectedPlatform,
      settlement.platformSettlements[selectedPlatform]!
    );
  } else if (payOutGroup.includes(selectedPlatform as Platform)) {
    rows = getPayOutRows(
      selectedPlatform,
      settlement.platformSettlements[selectedPlatform]!
    );
  } else if (kycGroup.includes(selectedPlatform as Platform)) {
    rows = getKycRows(settlement.platformSettlements[selectedPlatform]!);
  } else {
    rows = getSummaryRows(settlement);
  }

  return rows;
};
