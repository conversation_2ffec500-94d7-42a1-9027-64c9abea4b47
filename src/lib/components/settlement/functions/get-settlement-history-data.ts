import { getStatusColor } from "$lib/components/true-up/functions/status-color";
import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
import Badge from "$lib/components/ui/tools/Badge.svelte";
import { formatDate, formatDateStringToDDMMYYYY } from "$lib/utils/date-utils";

import type { Header, Row } from "$lib/components/ui/table/types";
import type { SettlementHistory } from "$lib/types/settlement/types";

const getTableRows = (rows: SettlementHistory[]): Row[] => {
  const tableRows: Row[] = [];

  for (const item of rows) {
    const newRow: Row = {
      id: item.jobId,
      showCheckbox: false,
      cells: [
        {
          component: ListText,
          props: {
            lines: [
              [
                {
                  text: `From: ${formatDateStringToDDMMYYYY(item.fromDate)}`,
                  classes: "dark:text-white",
                },
              ],
              [
                {
                  text: `To: ${formatDateStringToDDMMYYYY(item.toDate)}`,
                  classes: "dark:text-white",
                },
              ],
            ],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [{ text: item.userName, classes: "dark:text-white" }],
              [
                {
                  text: formatDate(new Date(item.generatedAt)),
                  classes: "text-gray-500 dark:text-gray-400 text-sm",
                },
              ],
              item?.meta?.timeTakenInSec && [
                {
                  text: `Duration: ${item.meta.timeTakenInSec.toFixed(
                    2
                  )} seconds`,
                  classes: "text-yellow-900 text-sm",
                },
              ],
            ],
          },
        },
        {
          component: Badge,
          props: {
            label: item.status,
            color: getStatusColor(item.status),
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              item?.meta &&
                item.status === "SUCCESS" && [
                  {
                    text: `Success: ${item.meta.successCount ?? "-"}`,
                    classes: "text-green-500",
                  },
                  {
                    text: `Total: ${item.meta.totalCount ?? "-"}`,
                    classes: "text-blue-500",
                  },
                ],
              item?.meta &&
                item.status === "SUCCESS" && [
                  {
                    text: `Skipped: ${item.meta.skippedCount ?? "-"}`,
                    classes: "text-yellow-500",
                  },
                  {
                    text: `Error: ${item.meta.errorCount ?? "-"}`,
                    classes: "text-red-500",
                  },
                ],
            ],
          },
        },
      ],
    };
    tableRows.push(newRow);
  }

  return tableRows;
};

const getTableHeaders = (): Header[] => {
  return [
    { id: "period", title: "Period", align: "left" },
    { id: "generateBy", title: "Generated By", align: "left" },
    { id: "status", title: "Status", align: "left" },
    { id: "stats", title: "Stats", align: "left" },
  ] as Header[];
};

export { getTableRows, getTableHeaders };
