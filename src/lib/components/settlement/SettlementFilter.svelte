<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import SettlementHistoryModal from "./modals/SettlementHistoryModal.svelte";
  import DateRangePicker from "../ui/date-pickers/DateRangePicker.svelte";
  import DropDownSelector from "../ui/drop-down/DropDownSelector.svelte";

  import Button from "$lib/components/ui/buttons/ButtonNew.svelte";
  import Filter from "$lib/components/ui/filter/Filter.svelte";
  import TextInput from "$lib/components/ui/inputs/TextInput.svelte";
  import { debounce } from "$lib/utils/debounce";

  import type { SettlementFilters } from "$lib/types/settlement/types";
  import type { DropdownItem } from "$lib/types/ui/drop-down/types";

  const dispatch = createEventDispatcher();

  export let clientTypes: string[];
  export let settlementStates: string[];
  export let frequencyTypes: string[];
  export let currentFilters: SettlementFilters;

  let settlementHistoryModalIsOpen = false;

  let filters: SettlementFilters = { ...currentFilters };

  // After loading all settlementStatuses we want to ensure we still filter
  // based on the dropdown selected earlier
  $: if (settlementStates) assignAvailableSettlementStates(filters.status);

  function assignAvailableSettlementStates(status: string) {
    generationStates = settlementStates.filter(
      (state) => !state.startsWith("Approval")
    );
    approvalStates = settlementStates.filter((state) =>
      state.startsWith("Approval")
    );

    if (status === "Settlement Approval") {
      dropdownSettlementStates = [...approvalStates];
    } else if (status === "Settlement Generation") {
      dropdownSettlementStates = [...generationStates];
    } else {
      dropdownSettlementStates = [...settlementStates];
    }

    // This is so that if the user selects a status and then changes it to
    // a status that does not contain items the current dropdown in state
    // is selected to, we clear it so that it doesn't look like there is a
    // empty state with the clear button
    if (!dropdownSettlementStates.includes(currentFilters.state)) {
      currentFilters.state = "";
    }
  }

  const settlementStatuses: DropdownItem[] = [
    {
      value: "Settlement Generation",
      label: "Settlement Generation",
      colorClass: "text-orange-500",
    },
    {
      value: "Settlement Approval",
      label: "Settlement Approval",
      colorClass: "text-blue-500",
    },
    {
      value: "No Filter",
      label: "No Filter",
    },
  ];

  const displayAdjustedOptions: string[] = [
    "Show All",
    "Adjustments Only",
    "No Adjustments",
  ];

  const baseFilters: SettlementFilters = {
    status: settlementStatuses[2].label,
    textInputValue: "",
    clientType: "",
    displayAdjusted: displayAdjustedOptions[0],
    state: "",
    frequency: "",
    startDate: undefined,
    endDate: undefined,
  };

  let hasActiveFilter = false;

  const filterKeys = Object.keys(baseFilters) as Array<keyof SettlementFilters>;
  $: hasActiveFilter = filterKeys.some(
    (key) => filters[key] !== baseFilters[key]
  );

  let dropdownSettlementStates = settlementStates;
  let generationStates = settlementStates.filter(
    (state) => !state.startsWith("Approval")
  );
  let approvalStates = settlementStates.filter((state) =>
    state.startsWith("Approval")
  );

  function handleGenerateSettlementsClicked() {
    dispatch("generateSettlementsClicked");
  }

  function handleFilterChange() {
    dispatch("filtersChange", {
      filters,
    });
  }

  function clearAllFilters() {
    filters = { ...baseFilters };
    dispatch("clearPreferences");
  }

  function handleStatusChange(event: CustomEvent<{ value: string }>) {
    filters.status = event.detail.value;
    assignAvailableSettlementStates(filters.status);
    handleFilterChange();
  }

  function handleTextInput() {
    filters.textInputValue = filters.textInputValue.trim();
    debounce(() => {
      handleFilterChange();
    }, 500);
  }

  function handleClientTypeChange(event: CustomEvent<{ value: string }>) {
    filters.clientType = event.detail.value;
    handleFilterChange();
  }

  function handleDisplayAdjustedChange(event: CustomEvent<{ value: string }>) {
    filters.displayAdjusted = event.detail.value;
    handleFilterChange();
  }

  function handleStateChange(event: CustomEvent<{ value: string }>) {
    filters.state = event.detail.value;
    handleFilterChange();
  }

  function handleFrequencyChange(event: CustomEvent<{ value: string }>) {
    filters.frequency = event.detail.value;
    handleFilterChange();
  }

  function handleDateChange() {
    handleFilterChange();
  }

  function handleSettlementHistoryClicked() {
    settlementHistoryModalIsOpen = true;
  }
</script>

<Filter on:clearFilters={clearAllFilters} {hasActiveFilter}>
  <div slot="filter-header-left-append">
    <div class="flex items-center gap-4">
      <div class="h-9 w-px bg-gray-200 dark:bg-neutral-700"></div>
      <DropDownSelector
        id="settlement-filter-status"
        items={settlementStatuses}
        value={filters.status}
        labelText={filters.status}
        on:change={handleStatusChange}
        clearable={false}
        classOverride="flex h-10 w-96 gap-2 rounded-md border shadow-sm shadow-gray-200
                       dark:shadow-none bg-white border-gray-300 hover:border-gray-400
                       dark:bg-neutral-800 dark:border-neutral-600 dark:hover:border-neutral-500"
        labelColor={settlementStatuses.find(
          (status) => status.label === filters.status
        )?.colorClass}
      />
    </div>
  </div>
  <div slot="filter-header-right-append">
    <div class="flex items-center gap-2">
      <Button
        intent="solid"
        id="settlement-history-modal-button"
        data-testid="settlement-history-modal-button"
        color="white"
        on:click={handleSettlementHistoryClicked}>Settlement History</Button
      >
      <Button
        intent="solid"
        color="green"
        on:click={handleGenerateSettlementsClicked}>Generate Settlements</Button
      >
    </div>
  </div>
  <div slot="content" class="2xl:flex grid grid-cols-3 gap-4 m-4">
    <div class="flex-1">
      <TextInput
        id="settlement-filter-text-input"
        label="Name / Service #"
        placeholder="Enter a Client Name or Service #"
        bind:value={filters.textInputValue}
        on:input={handleTextInput}
      />
    </div>
    <div class="flex-1">
      <DropDownSelector
        id="settlement-filter-client-type-dropdown"
        label="Client Type"
        placeholder="Select Client Type"
        items={clientTypes}
        value={filters.clientType}
        labelText={filters.clientType}
        on:change={handleClientTypeChange}
        clearable={true}
        on:clear={() => {
          filters.clientType = "";
          handleFilterChange();
        }}
      />
    </div>
    <div class="flex-1">
      <DropDownSelector
        id="settlement-filter-display-adjusted-dropdown"
        label="Display Adjusted Settlements"
        placeholder="Select Display Adjusted Settlements"
        items={displayAdjustedOptions}
        value={filters.displayAdjusted}
        labelText={filters.displayAdjusted}
        on:change={handleDisplayAdjustedChange}
        on:clear={() => {
          filters.displayAdjusted = "Show All";
          handleFilterChange();
        }}
      />
    </div>
    <div class="flex-1">
      <DropDownSelector
        id="settlement-filter-state-dropdown"
        label="Settlement State"
        placeholder="Select Settlement State"
        items={dropdownSettlementStates}
        value={filters.state}
        labelText={filters.state}
        on:change={handleStateChange}
        clearable={true}
        on:clear={() => {
          filters.state = "";
          handleFilterChange();
        }}
      />
    </div>
    <div class="flex-1">
      <DropDownSelector
        id="settlement-filter-frequency-dropdown"
        label="Settlement Frequency"
        placeholder="Select Settlement Frequency"
        items={frequencyTypes}
        value={filters.frequency}
        labelText={filters.frequency}
        on:change={handleFrequencyChange}
        clearable={true}
        on:clear={() => {
          filters.frequency = "";
          handleFilterChange();
        }}
      />
    </div>
    <div class="flex-1">
      <div class="mb-1.5">
        <label
          for="settlement-filter-date-picker"
          class="text-sm font-medium text-gray-800 dark:text-white"
        >
          From Date / To Date
        </label>
      </div>
      <DateRangePicker
        id="settlement-filter-date-picker"
        placeholder="Select Date"
        bind:startDate={filters.startDate}
        bind:endDate={filters.endDate}
        on:change={handleDateChange}
      />
    </div>
  </div>
</Filter>

<SettlementHistoryModal bind:showModal={settlementHistoryModalIsOpen} />
