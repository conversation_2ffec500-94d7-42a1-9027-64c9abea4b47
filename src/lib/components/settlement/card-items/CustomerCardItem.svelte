<script lang="ts">
  export let customerName = "";
  export let customerType: "Merchant" | "Integrator" | "Agent" | "Sub Agent";
  export let serviceNumber = "";

  const entityNumber = serviceNumber.slice(0, 3);
  const remainingServiceNumber = serviceNumber.slice(3);
</script>

<div
  class="border border-grey-200 rounded p-4 pr-8 flex flex-col gap-3 w-64 flex-shrink-0
       dark:border-neutral-600 dark:bg-neutral-700"
>
  <p
    class="border border-grey-200 rounded drop-shadow-sm py-0.5 px-2 max-w-max
           text-xs text-gray-800 dark:text-white dark:border-neutral-600 dark:bg-neutral-600"
  >
    {customerType}
  </p>
  <div class="flex flex-col gap-1 dark:text-white">
    <h3 class="truncate leading-[normal]" title={customerName}>
      {customerName}
    </h3>
    <p>
      <span class="text-blue-600">{entityNumber}</span>{remainingServiceNumber}
    </p>
  </div>
</div>
