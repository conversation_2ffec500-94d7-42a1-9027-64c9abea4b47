<script lang="ts">
  export let fromDate = "";
  export let toDate = "";

  const formatDate = (date: string) => {
    const [year, month, day] = date.split("-");

    return `${day}-${month}-${year}`;
  };
</script>

<div class="flex flex-col gap-3 px-4 py-2 flex-shrink-0">
  <div class="flex flex-col gap-1">
    <small class="text-xs">From Date</small>
    <p class="dark:text-white">{formatDate(fromDate)}</p>
  </div>
  <div class="flex flex-col gap-1">
    <small class="text-xs">To Date</small>
    <p class="dark:text-white">{formatDate(toDate)}</p>
  </div>
</div>
