<script lang="ts">
  import Button from "$lib/components/ui/buttons/ButtonNew.svelte";
  import {
    platforms,
    type PlatformSettlements,
  } from "$lib/types/settlement/types";

  export let platformSettlements: PlatformSettlements = {};
</script>

<div class="flex flex-col gap-2 px-4 max-w-md flex-shrink-0">
  <small class="text-xs">Payment Platforms</small>
  <div class="flex flex-wrap gap-2">
    {#each platforms as platform}
      {#if platformSettlements[platform]?.isAdjusted}
        <Button
          intent="solid"
          size="small"
          color="purple"
          className="rounded-full px-3 py-2"
        >
          {platform}
        </Button>
      {:else if platformSettlements[platform]?.isNonZero}
        <Button
          intent="solid"
          size="small"
          color="blue"
          className="rounded-full px-3 py-2"
        >
          {platform}
        </Button>
      {:else}
        <Button
          intent="outline"
          size="small"
          color="dark"
          className="rounded-full px-3 py-2 dark:bg-white dark:text-gray-800 
                     dark:hover:bg-gray-200 dark:focus:bg-gray-200 dark:hover:text-gray-600"
        >
          {platform}
        </Button>
      {/if}
    {/each}
  </div>
</div>
