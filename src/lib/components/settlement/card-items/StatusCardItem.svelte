<script lang="ts">
  /* eslint-disable @typescript-eslint/naming-convention */

  import Button from "$lib/components/ui/buttons/ButtonNew.svelte";

  import type { SettlementStatus } from "$lib/types/settlement/types";

  export let status: SettlementStatus;

  $: statusColor = {
    Processing: "dark",
    Skipped: "gray",
    Error: "red",
    "Approval Pending": "blue",
    "Approval Processing": "dark",
    "Approval Error": "red",
    "Approval Success": "green",
    "Status Unknown": "white",
  }[status] as "dark" | "gray" | "red" | "blue" | "green" | "white";
</script>

<div class="flex flex-col gap-1 px-4 py-2">
  <small class="text-xs">Settlement Status</small>
  <Button color={statusColor} className="rounded-full p-2.5 gap-1 w-44">
    <p class="text-xs px-1">
      {status}
    </p>
    <i class="bi bi-question-circle" />
  </Button>
</div>
