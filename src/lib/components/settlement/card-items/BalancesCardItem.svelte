<script lang="ts">
  import { formatCurrency } from "$lib/utils/format-currency";

  export let netPayout = "0";
  export let endBalance = "0";
</script>

<div class="flex flex-col gap-3 px-4 py-2 flex-shrink-0 w-44">
  <div class="flex flex-col gap-1">
    <small class="text-xs">Net Payout</small>
    <p class="dark:text-white">{formatCurrency(netPayout)}</p>
  </div>
  <div class="flex flex-col gap-1">
    <small class="text-xs">End Balance</small>
    <p class="dark:text-white">
      {Number.isNaN(Number.parseFloat(endBalance))
        ? "Not Found"
        : formatCurrency(endBalance)}
    </p>
  </div>
</div>
