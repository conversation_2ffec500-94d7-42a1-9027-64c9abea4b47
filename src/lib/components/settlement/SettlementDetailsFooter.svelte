<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import { goto } from "$app/navigation";
  import Button from "$lib/components/ui/buttons/ButtonNew.svelte";
  import { formatCurrency } from "$lib/utils/format-currency";

  import type {
    PlatformCode,
    SettlementSummary,
  } from "$lib/types/settlement/types";

  export let settlement: SettlementSummary;
  export let selectedPlatform: PlatformCode;

  const dispatch = createEventDispatcher();

  const handleDelete = () => {
    dispatch("delete");
  };
</script>

<div>
  <div
    class="px-4 py-2 border-t border-gray-200 flex items-center dark:text-white justify-end gap-4"
  >
    {#if selectedPlatform === "SUMMARY"}
      <div>
        <span class="font-medium">Total Payout</span>
        <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
          {formatCurrency(
            settlement.platformSettlements.SUMMARY?.totalPayout ?? 0
          )}
        </div>
      </div>
      <div>
        <span class="font-medium">Total Adjustments</span>
        <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
          {formatCurrency(
            settlement.platformSettlements.SUMMARY?.totalAdjustments ?? 0
          )}
        </div>
      </div>
      <div>
        <span class="font-medium">Net Payout</span>
        <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
          {formatCurrency(
            settlement.platformSettlements.SUMMARY?.netPayout ?? 0
          )}
        </div>
      </div>
      <div>
        <span class="font-medium">End Balance</span>
        <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
          {formatCurrency(
            settlement.platformSettlements.SUMMARY?.endBalance ?? 0
          )}
        </div>
      </div>
    {:else}
      <div>
        <span class="font-medium">Total Transaction</span>
        <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
          {formatCurrency(
            (settlement.platformSettlements[selectedPlatform]
              ?.totalTransactionAmount ?? 0) -
              (settlement.platformSettlements[selectedPlatform]
                ?.totalFailedAmount ?? 0)
          )}
        </div>
      </div>
      <div>
        <span class="font-medium">Total Costs</span>
        <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
          {formatCurrency(
            settlement.platformSettlements[selectedPlatform]?.totalCosts ?? 0
          )}
        </div>
      </div>
      <div>
        <span class="font-medium">Total Adjustments</span>
        <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
          {formatCurrency(
            settlement.platformSettlements[selectedPlatform]
              ?.totalAdjustments ?? 0
          )}
        </div>
      </div>
      <div>
        <span class="font-medium">Total Payable</span>
        <div class="mt-2 border dark:border-neutral-600 rounded-md w-40 p-2">
          {formatCurrency(
            settlement.platformSettlements[selectedPlatform]?.totalPayable ?? 0
          )}
        </div>
      </div>
    {/if}
  </div>
  <div
    class="border-t px-4 py-2 flex items-center justify-between dark:border-neutral-600"
  >
    <div>
      <Button
        className="rounded-full"
        intent="solid"
        color="white"
        on:click={async () => {
          await goto("/settlement");
        }}>Back</Button
      >
    </div>

    <div class="flex items-center gap-4">
      <Button intent="solid" color="white">Visit Email</Button>
      <div>
        <Button intent="outline" color="green">Approve</Button>
        <Button intent="outline" color="blue">Regenerate</Button>
        <Button intent="outline" color="red" on:click={handleDelete}
          >Delete</Button
        >
      </div>
    </div>
  </div>
</div>
