<script lang="ts">
  import Table from "$lib/components/ui/table/Table.svelte";

  import type { Header, Row } from "$lib/components/ui/table/types";

  export let headers: Header[];
  export let rows: Row[];
  export let selectedRows: Set<number>;
  export let onRowClick: (event: CustomEvent<{ rowId: number }>) => void;
</script>

<Table {headers} {rows} bind:selectedRows on:rowSingleClick={onRowClick} />
