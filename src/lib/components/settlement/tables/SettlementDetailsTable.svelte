<script lang="ts">
  import { getTableHeaders } from "../functions/get-details-table-headers";
  import { getTableRows } from "../functions/get-details-table-rows";

  import Table from "$lib/components/ui/table/Table.svelte";

  import type {
    PlatformCode,
    SettlementSummary,
  } from "$lib/types/settlement/types";

  export let selectedPlatform: PlatformCode;
  export let settlement: SettlementSummary;

  $: headers = getTableHeaders(selectedPlatform);
  $: rows = getTableRows(selectedPlatform, settlement);
</script>

<Table {headers} {rows} />
