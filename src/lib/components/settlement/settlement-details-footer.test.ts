/* eslint-disable @typescript-eslint/naming-convention */
import { render, fireEvent, screen } from "@testing-library/svelte";
import { describe, it, expect, vi, beforeEach } from "vitest";

import SettlementDetailsFooter from "./SettlementDetailsFooter.svelte";

import type { SettlementSummary } from "$lib/types/settlement/types";

let gotoMock: ReturnType<typeof vi.fn>;
vi.mock("$app/navigation", () => ({
  get goto() {
    return gotoMock;
  },
}));

const baseSettlement: SettlementSummary = {
  id: "1",
  customerName: "Test Customer",
  serviceNumber: "123",
  customerType: "TypeA",
  status: "Processing",
  netPayout: "1000",
  endBalance: "5000",
  fromDate: "2024-01-01",
  toDate: "2024-01-31",
  platformSettlements: {
    SUMMARY: {
      labelName: "SUMMARY",
      isNonZero: true,
      transactionCount: 10,
      totalTransactionAmount: 2000,
      refundCount: 1,
      totalRefundAmount: 100,
      gatewayFee: 10,
      transactionFee: 20,
      salesFee: 5,
      refundFee: 2,
      totalFailedAmount: 50,
      endBalance: 5000,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 0,
      minimumFeeCount: 0,
      totalMinimumAmount: 0,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      totalPayable: 900,
      isAdjusted: false,
      totalPayout: 1200,
      totalAdjustments: 100,
      netPayout: 1100,
      totalCosts: 200,
    },
    IDP: {
      labelName: "IDP",
      isNonZero: true,
      transactionCount: 5,
      totalTransactionAmount: 1000,
      refundCount: 0,
      totalRefundAmount: 0,
      gatewayFee: 5,
      transactionFee: 10,
      salesFee: 2,
      refundFee: 1,
      totalFailedAmount: 20,
      endBalance: 2500,
      total2FaRejectAmount: 0,
      total2FaRejectCount: 0,
      txnAmountRTO_R: 0,
      txnCountETI_R1: 0,
      minimumFeeTotal: 0,
      minimumFeeCount: 0,
      totalMinimumAmount: 0,
      partialReturnAmountRTO: 0,
      partialReturnCountRTO: 0,
      totalPayable: 800,
      isAdjusted: false,
      totalPayout: 900,
      totalAdjustments: 50,
      netPayout: 850,
      totalCosts: 100,
    },
  },
};

describe("SettlementDetailsFooter", () => {
  beforeEach(() => {
    gotoMock = vi.fn();
  });

  it("renders summary values when selectedPlatform is SUMMARY", () => {
    render(SettlementDetailsFooter, {
      props: {
        settlement: baseSettlement,
        selectedPlatform: "SUMMARY",
      },
    });
    expect(screen.getByText("Total Payout")).toBeInTheDocument();
    expect(screen.getByText("Total Adjustments")).toBeInTheDocument();
    expect(screen.getByText("Net Payout")).toBeInTheDocument();
    expect(screen.getByText("End Balance")).toBeInTheDocument();
    expect(screen.getByText("$1,200.00")).toBeInTheDocument();
    expect(screen.getByText("$100.00")).toBeInTheDocument();
    expect(screen.getByText("$1,100.00")).toBeInTheDocument();
  });

  it("renders platform values when selectedPlatform is not SUMMARY", () => {
    render(SettlementDetailsFooter, {
      props: {
        settlement: baseSettlement,
        selectedPlatform: "IDP",
      },
    });
    expect(screen.getByText("Total Transaction")).toBeInTheDocument();
    expect(screen.getByText("Total Costs")).toBeInTheDocument();
    expect(screen.getByText("Total Adjustments")).toBeInTheDocument();
    expect(screen.getByText("Total Payable")).toBeInTheDocument();
    expect(screen.getByText("$980.00")).toBeInTheDocument();
    expect(screen.getByText("$100.00")).toBeInTheDocument();
    expect(screen.getByText("$50.00")).toBeInTheDocument();
    expect(screen.getByText("$800.00")).toBeInTheDocument();
  });

  it("calls goto on Back button click", async () => {
    render(SettlementDetailsFooter, {
      props: {
        settlement: baseSettlement,
        selectedPlatform: "SUMMARY",
      },
    });
    const backButton = screen.getByText("Back");
    await fireEvent.click(backButton);
    expect(gotoMock).toHaveBeenCalledWith("/settlement");
  });
});
