<script lang="ts">
  import { onMount } from "svelte";

  import { getTableRows, getTableHeaders } from "./functions/roles-table-data";
  import AddRoleModal from "./modals/AddRoleModal.svelte";

  import RoleAside from "$lib/components/admin/iam/fragments/RolesAside.svelte";
  import Button from "$lib/components/ui/buttons/Button.svelte";
  import NoData from "$lib/components/ui/common/NoData.svelte";
  import {
    failureToast,
    successToast,
  } from "$lib/components/ui/notifications/toast";
  import Table from "$lib/components/ui/table/Table.svelte";
  import { type Header, type Row } from "$lib/components/ui/table/types";
  import { request } from "$lib/services/api-caller/request";
  import { type Role, type RoleWithUsers } from "$lib/types/user-profile";
  import { type RequestError } from "$lib/utils/errors/request-error";

  const headers: Header[] = getTableHeaders();
  let tableRowData: Row[] = [];
  let selectedRole: Role | undefined;

  let resources: Record<string, string>;
  let permissions: Record<string, string>;
  let resourcesRule: Record<string, string[]>;
  let roles: RoleWithUsers[];

  let isAddRoleModalOpen = false;

  onMount(async () => {
    await loadData();

    if (roles.length > 0) {
      selectedRole = roles[0];
    }
  });

  async function loadData() {
    try {
      // Fetch both requests in parallel
      const [resourcesResponse, rolesResponse] = await Promise.all([
        request.get<{
          resources: Record<string, string>;
          permissions: Record<string, string>;
          resourcesRule: Record<string, string[]>;
        }>("/access/resources-rule"),

        request.get<{
          roles: RoleWithUsers[];
        }>("/access/role"),
      ]);

      resources = resourcesResponse.resources;
      permissions = resourcesResponse.permissions;
      resourcesRule = resourcesResponse.resourcesRule;
      roles = rolesResponse?.roles || [];
      tableRowData = getTableRows(roles);
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(
        requestError?.message ?? "Failed to fetch the initial role data."
      );
    }
  }

  function handleRowSelect(event: CustomEvent<{ rowId: number }>): void {
    const { rowId } = event.detail;
    selectedRole = roles.find((role) => role.id === rowId);
  }

  async function handleDeleteRoleConfirm(
    event: CustomEvent<{ roleId: number }>
  ) {
    const { roleId } = event.detail;

    try {
      await request.delete<undefined>(`/access/role`, {
        roleId,
      });
      await loadData();
      selectedRole = roles?.length > 0 ? roles[0] : undefined;
      successToast("Role was deleted successfully.");
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to delete the role.");
    }
  }

  async function handleUpdateRoleConfirm(
    event: CustomEvent<{
      roleId: number;
      roleName: string;
      roleRule: Record<string, string[]>;
    }>
  ) {
    const { roleId, roleName, roleRule } = event.detail;

    if (!roleName) {
      failureToast("Role name cannot be empty");

      return;
    }

    try {
      await request.put<undefined>(`/access/role`, {
        roleId,
        name: roleName,
        rule: roleRule,
      });
      successToast("Role was updated successfully.");
      await loadData();
      selectedRole = roles.find((role) => role.id === roleId);
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to update the role.");
    }
  }

  async function handleAddRoleConfirm(event: CustomEvent<{ name: string }>) {
    const { name } = event.detail;

    if (!name) {
      failureToast("Role name cannot be empty.");

      return;
    }

    try {
      isAddRoleModalOpen = false;
      const response = await request.post<{ roleId: number }>("/access/role", {
        name: name.trim(),
      });
      await loadData();
      selectedRole = roles.find((role) => role.id === response.roleId);
      successToast("Role was added successfully.");
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to add the role.");
    }
  }
</script>

<section
  id="iam-role-page"
  data-testid="iam-role-page"
  class="grid h-full w-full grid-cols-3"
>
  <div class="col-span-1 flex h-full flex-col border-r overflow-auto">
    <div class="flex w-full justify-end border-b p-2">
      <div class=" w-36">
        <Button
          id="role-add-open-modal"
          label={"Add Role"}
          on:click={() => {
            isAddRoleModalOpen = true;
          }}
        >
          <i class="bi bi-plus-circle" slot="icon" />
        </Button>
      </div>
    </div>

    {#if roles?.length === 0}
      <NoData />
    {:else}
      {#key roles}
        <Table
          {headers}
          rows={tableRowData}
          selectedRows={new Set(selectedRole ? [selectedRole.id] : [])}
          on:rowSingleClick={handleRowSelect}
        />
      {/key}
    {/if}
  </div>
  {#if selectedRole}
    <RoleAside
      {resources}
      {permissions}
      {resourcesRule}
      bind:role={selectedRole}
      on:delete={handleDeleteRoleConfirm}
      on:update={handleUpdateRoleConfirm}
    />
  {/if}
  {#if isAddRoleModalOpen}
    <AddRoleModal
      bind:showModal={isAddRoleModalOpen}
      on:confirm={handleAddRoleConfirm}
    />
  {/if}
</section>
