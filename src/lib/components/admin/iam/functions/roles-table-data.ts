import IconOrText from "$lib/components/ui/table/common-cell-components/IconOrText.svelte";
import { type Row, type Header } from "$lib/components/ui/table/types";
import Badge from "$lib/components/ui/tools/Badge.svelte";
import { type RoleWithUsers } from "$lib/types/user-profile";

const getTableRows = (roles: RoleWithUsers[]): Row[] => {
  return roles.map((role) => ({
    id: role.id,
    cells: [
      role.name,
      role?.rule && Object.keys(role?.rule).length > 0
        ? ""
        : {
            component: Badge,
            props: {
              label: "PENDING",
              type: "PENDING",
            },
          },
      role.users.length.toString(),
      {
        component: IconOrText,
        props: {
          right: { text: "bi bi-chevron-right", isIcon: true },
          space: 4,
        },
      },
    ],
  }));
};

const getTableHeaders = (): Header[] => {
  return [
    { id: "role", title: "Role", align: "left" },
    { id: "badge", title: "", align: "left" },
    { id: "usersCount", title: "# of Users", align: "center" },
    { id: "action", title: "", align: "center" },
  ];
};

export { getTableRows, getTableHeaders };
