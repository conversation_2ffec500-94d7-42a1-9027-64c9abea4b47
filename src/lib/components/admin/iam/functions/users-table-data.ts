import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
import { type Row } from "$lib/components/ui/table/types";
import { type User } from "$lib/types/user-profile";

const getTableRows = (users: User[]): Row[] => {
  return users.map((user) => ({
    id: user.id,
    cells: [
      {
        component: ListText,
        props: {
          lines: [
            [{ text: user.fullName }],
            [
              {
                text: user.email,
                classes: "text-gray-500 text-sm",
              },
            ],
          ],
        },
      },
    ],
  }));
};

export { getTableRows };
