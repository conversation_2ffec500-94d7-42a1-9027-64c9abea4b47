import Checkbox from "$lib/components/ui/inputs/Checkbox.svelte";
import { type Row, type Header } from "$lib/components/ui/table/types";

const getTableRows = (
  resources: Record<string, string>,
  permissions: Record<string, string>,
  resourcesRule: Record<string, string[]>,
  roleRule: Record<string, string[]>,
  isEditable: boolean,
  onToggle: (id: string, checked: boolean) => void
  // eslint-disable-next-line max-params
): Row[] => {
  return Object.entries(resources).map(([resourceKey, resourceValue]) => ({
    id: resourceKey,
    cells: [
      resourceValue,
      ...Object.keys(permissions).map((permissionKey) => ({
        component: Checkbox,
        props: {
          id: `${resourceKey}-${permissionKey}`,
          visible: resourcesRule[resourceKey]?.includes(permissionKey) ?? false,
          checked: roleRule[resourceKey]?.includes(permissionKey) ?? false,
          isEditable,
          onToggle,
        },
      })),
    ],
  }));
};

const getTableHeaders = (permissions: Record<string, string>): Header[] => {
  const headers: Header[] = [
    { id: "resources", title: "Resources", align: "left" },
  ];

  for (const [key, value] of Object.entries(permissions)) {
    headers.push({ id: key, title: value, align: "center" });
  }

  return headers;
};

export { getTableRows, getTableHeaders };
