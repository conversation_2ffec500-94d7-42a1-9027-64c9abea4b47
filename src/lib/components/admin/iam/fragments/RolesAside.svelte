<script lang="ts">
  import RolePermissionMatrix from "./RolePermissionMatrix.svelte";
  import RoleUsers from "./RoleUsers.svelte";

  import TabSelector from "$lib/components/ui/common/TabSelector.svelte";
  import { type RoleWithUsers } from "$lib/types/user-profile";

  export let resources: Record<string, string> = {};
  export let permissions: Record<string, string> = {};
  export let resourcesRule: Record<string, string[]> = {};
  export let role: RoleWithUsers | undefined;

  const headerTabs = ["Rule", "Users"];
  let headerSelection = headerTabs[0];
</script>

<div class="col-span-2 flex flex-col">
  <div
    class="flex justify-between border-b p-6 text-sm"
    id="role-aside-header-selector"
  >
    {#each headerTabs as tab}
      <TabSelector label={tab} bind:selected={headerSelection} />
    {/each}
  </div>

  {#if headerSelection === headerTabs[0]}
    <RolePermissionMatrix
      {resources}
      {permissions}
      {resourcesRule}
      bind:role
      on:delete
      on:update
    />
  {/if}

  {#if headerSelection === headerTabs[1]}
    <RoleUsers users={role?.users ?? []} />
  {/if}
</div>
