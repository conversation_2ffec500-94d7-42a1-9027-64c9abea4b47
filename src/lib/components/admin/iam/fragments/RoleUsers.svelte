<script lang="ts">
  import { getTableRows } from "../functions/users-table-data";

  import { goto } from "$app/navigation";
  import Table from "$lib/components/ui/table/Table.svelte";
  import { type User } from "$lib/types/user-profile";

  export let users: User[] = [];

  async function handleTableRowDoubleClick(
    event: CustomEvent<{ rowId: number }>
  ) {
    const { rowId } = event.detail;
    const name = users.find((user) => user.id === rowId)?.fullName ?? "";
    await goto(`/users?name=${encodeURIComponent(name)}`);
  }
</script>

<div>
  <Table
    rows={getTableRows(users)}
    on:rowDoubleClick={handleTableRowDoubleClick}
  />
</div>
