<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import {
    getTableHeaders,
    getTableRows,
  } from "../functions/matrix-table-data";
  import DeleteRoleModal from "../modals/DeleteRoleModal.svelte";
  import UpdateRoleModal from "../modals/UpdateRoleModal.svelte";

  import Button from "$lib/components/ui/buttons/Button.svelte";
  import Container from "$lib/components/ui/common/Container.svelte";
  import TextInput from "$lib/components/ui/inputs/TextInput.svelte";
  import Table from "$lib/components/ui/table/Table.svelte";
  import { type Role } from "$lib/types/user-profile";

  export let resources: Record<string, string> = {};
  export let permissions: Record<string, string> = {};
  export let resourcesRule: Record<string, string[]> = {};
  export let role: Role | undefined;
  let isDeleteRoleModalOpen = false;
  let isUpdateRoleModalOpen = false;

  // `initialRoleName` tracks the role's original name and updates when the `role` prop changes.
  // `roleName` is initially set to `initialRoleName` but can be edited locally by the user.
  // This prevents local updates to `roleName` from being overridden by reactive updates unless
  // the `role` changes.
  let initialRoleName = "";
  let roleRule: Record<string, string[]> = {};

  $: {
    roleRule = role?.rule ?? {};
    initialRoleName = role?.name ?? "";
  }

  $: roleName = initialRoleName;

  $: headers = getTableHeaders(permissions);
  $: tableRowData = getTableRows(
    resources,
    permissions,
    resourcesRule,
    role?.rule ?? {},
    Boolean(role?.isEditable),
    handleRoleMatrixChange
  );

  const dispatch = createEventDispatcher();

  function handleRoleMatrixChange(id: string, checked: boolean) {
    const [resourceKey, permissionKey] = id.split("-");

    const permissionsSet = roleRule[resourceKey]
      ? new Set(roleRule[resourceKey])
      : new Set<string>();

    if (checked) {
      permissionsSet.add(permissionKey);
    } else {
      permissionsSet.delete(permissionKey);
    }

    roleRule = {
      ...roleRule,
      [resourceKey]: [...permissionsSet],
    };
  }
  function handleDeleteRoleConfirm() {
    isDeleteRoleModalOpen = false;
    dispatch("delete", { roleId: role?.id });
  }
  function handleUpdateRoleConfirm() {
    isUpdateRoleModalOpen = false;
    roleName = roleName?.trim();

    dispatch("update", {
      roleId: role?.id,
      roleName,
      roleRule,
    });
  }
</script>

<div class="col-span-2 flex flex-col h-full">
  <!--Aside-->
  <section
    class=" scrollbar flex h-16 grow flex-col gap-4 overflow-auto p-4 pb-40"
  >
    <TextInput
      id="permission-aside-role-name-input"
      label="Role Name"
      isRequired={true}
      isDisabled={!role?.isEditable}
      bind:value={roleName}
    />
    <Container
      id="permissions-container"
      title="Permissions"
      subtitle="Impacts all users with associated role"
      isWhite={true}
    >
      <Table slot="content" {headers} bind:rows={tableRowData} />
    </Container>
  </section>
  <div class="flex flex-row gap-4 border-t p-4">
    <Button
      id="apply-role-button"
      color="black"
      label="Apply"
      on:click={() => {
        isUpdateRoleModalOpen = true;
      }}
      isDisabled={!role?.isEditable}
    />

    <Button
      id="delete-role-button"
      color="red"
      label="Delete"
      on:click={() => {
        isDeleteRoleModalOpen = true;
      }}
      isDisabled={!role?.isEditable}
    />
  </div>
  <UpdateRoleModal
    bind:showModal={isUpdateRoleModalOpen}
    name={initialRoleName}
    on:confirm={handleUpdateRoleConfirm}
  />
  <DeleteRoleModal
    bind:showModal={isDeleteRoleModalOpen}
    name={initialRoleName}
    on:confirm={handleDeleteRoleConfirm}
  />
</div>
