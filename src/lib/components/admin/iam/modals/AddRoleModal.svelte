<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import TextInput from "$lib/components/ui/inputs/TextInput.svelte";
  import Modal from "$lib/components/ui/modals/Modal.svelte";

  export let showModal: boolean;
  let name = "";

  const dispatch = createEventDispatcher();

  function handleConfirm() {
    dispatch("confirm", { name });
  }
</script>

<Modal
  title="Add Role"
  id="id-add-role-modal"
  bind:showModal
  on:confirm={handleConfirm}
>
  <div slot="content" class="py-2">
    <TextInput label="Role Name" bind:value={name} />
  </div>
</Modal>
