<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import Button from "$lib/components/ui/buttons/Button.svelte";
  import MultiSelectDropDown from "$lib/components/ui/drop-down/MultiSelectDropDown.svelte";
  import CopyTextInput from "$lib/components/ui/inputs/CopyTextInput.svelte";
  import Modal from "$lib/components/ui/modals/Modal.svelte";
  import { type UserWithRoles, type Role } from "$lib/types/user-profile";

  import type { DropdownItem } from "$lib/types/ui/drop-down/types";

  export let user: UserWithRoles;
  export let roles: Role[];

  let userId: number | undefined;
  let selectedItems: DropdownItem[] = [];

  $: if (user && user.id !== userId) {
    userId = user.id;
    selectedItems = user.roles.map((role) => ({
      value: role.id,
      label: role.name,
    }));
  }

  $: roleItems = roles.map(
    (role) =>
      ({
        value: role.id,
        label: role.name,
      }) satisfies DropdownItem
  );

  let isUpdateUserModalOpen = false;
  const dispatch = createEventDispatcher();

  function handleUpdateUserConfirm() {
    isUpdateUserModalOpen = false;

    const initialRoleIds: number[] = user.roles.map((role) => role.id);

    const selectedRoleIds: number[] = selectedItems.map(
      (item) => item.value as number
    );

    // Determine added roles
    const addRoleIds = selectedRoleIds.filter(
      (id) => !initialRoleIds.includes(id)
    );

    // Determine removed roles
    const removeRoleIds = initialRoleIds.filter(
      (id) => !selectedRoleIds.includes(id)
    );

    dispatch("updateUser", {
      userId: user.id,
      addRoleIds,
      removeRoleIds,
    });
  }

  function handleClose() {
    dispatch("closeAside", { userId: user.id });
  }
</script>

<div class="h-full w-full p-4 border-l border-gray-200 flex flex-col">
  <div class="flex-1 overflow-y-auto">
    <CopyTextInput
      id="user-account-username"
      label="Username"
      value={user.fullName ?? ""}
    />

    <div class="my-4">
      <CopyTextInput
        id="user-account-email"
        label="Email"
        value={user.email ?? ""}
      />
    </div>
    <MultiSelectDropDown
      id="user-roles"
      label="Roles"
      placeholder="Select roles"
      clearable={true}
      items={roleItems}
      bind:selectedItems
    />
  </div>

  <div class="flex flex-row gap-4 border-t p-4 flex-none">
    <Button
      id="apply-user-button"
      color="black"
      label="Apply"
      on:click={() => {
        isUpdateUserModalOpen = true;
      }}
    />
    <Button
      id="close-aside-button"
      color="grey-text"
      label="Close"
      on:click={handleClose}
    />
  </div>
  <Modal
    title="Update User"
    subtitle={`Are you sure you want to update user - "${user.fullName}"?`}
    id="id-update-user-modal"
    bind:showModal={isUpdateUserModalOpen}
    on:confirm={handleUpdateUserConfirm}
  ></Modal>
</div>
