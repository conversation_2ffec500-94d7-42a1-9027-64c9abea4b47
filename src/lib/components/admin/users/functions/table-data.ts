import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
import { type Row, type Header } from "$lib/components/ui/table/types";
import { type UserWithRoles } from "$lib/types/user-profile";

const getTableRows = (users: UserWithRoles[]): Row[] => {
  return users.map((user) => ({
    id: user.id,
    cells: [
      {
        component: ListText,
        props: {
          lines: [
            [{ text: user.fullName }],
            [
              {
                text: user.email,
                classes: "text-gray-500 text-sm",
              },
            ],
          ],
        },
      },
      user.roles.map((userRole) => userRole.name).join(", "),
    ],
  }));
};

const getTableHeaders = (): Header[] => {
  return [
    { id: "name", title: "Name", align: "left" },
    { id: "role", title: "Roles", align: "center" },
  ];
};

export { getTableRows, getTableHeaders };
