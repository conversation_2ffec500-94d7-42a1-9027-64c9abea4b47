<script lang="ts">
  import { onMount } from "svelte";

  import UserAside from "./fragments/UserAside.svelte";
  import { getTableRows, getTableHeaders } from "./functions/table-data";
  import {
    type UserWithRoles,
    type RoleWithUsers,
  } from "../../../types/user-profile";

  import { goto } from "$app/navigation";
  import { page } from "$app/stores";
  import Button from "$lib/components/ui/buttons/Button.svelte";
  import Filter from "$lib/components/ui/filter/Filter.svelte";
  import TextInput from "$lib/components/ui/inputs/TextInput.svelte";
  import {
    failureToast,
    successToast,
  } from "$lib/components/ui/notifications/toast";
  import PaginationFooter from "$lib/components/ui/pagination/PaginationFooter.svelte";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import Table from "$lib/components/ui/table/Table.svelte";
  import { type Row } from "$lib/components/ui/table/types";
  import { request } from "$lib/services/api-caller/request";
  import { debounce } from "$lib/utils/debounce";
  import { type RequestError } from "$lib/utils/errors/request-error";

  let isLoading = false;
  let currentPage = 1;
  let recordsPerPage = 20;
  let totalCount = 0;
  let selectedRows = new Set<number>();

  let nameInputValue = "";

  let filters: Partial<{
    name: string | undefined;
    email: string | undefined;
  }> = {};

  const headers = getTableHeaders();
  let tableRows: Row[] = [];
  let sourceUsers: UserWithRoles[] = [];
  let roles: RoleWithUsers[] = [];

  $: lastPage = Math.ceil(totalCount / recordsPerPage);
  $: hasActiveFilter = Boolean(nameInputValue);
  $: selectedUser =
    selectedRows.size > 0
      ? sourceUsers.find((user) => user.id === [...selectedRows][0])
      : undefined;

  let previousSearch = $page.url.search;
  $: if ($page.url.search !== previousSearch) {
    previousSearch = $page.url.search;
    const parameters = $page.url.searchParams;
    nameInputValue = parameters.get("name") ?? "";
    filters = { name: nameInputValue };
    // eslint-disable-next-line unicorn/prefer-top-level-await
    void loadUsers();
  }

  onMount(async () => {
    const parameters = $page.url.searchParams;
    nameInputValue = parameters.get("name") ?? "";
    filters = { name: nameInputValue };
    await loadUsers();
  });

  async function loadUsers() {
    try {
      const offset = (currentPage - 1) * recordsPerPage;
      isLoading = true;

      const queryParameters = new URLSearchParams({
        offset: offset.toString(),
        limit: recordsPerPage.toString(),
      });

      if (filters.name) {
        queryParameters.set("name", filters.name);
      }

      const [usersResponse, rolesResponse] = await Promise.all([
        request.get<{
          users: UserWithRoles[];
          totalCount: number;
        }>(`/users?${queryParameters.toString()}`),

        request.get<{
          roles: RoleWithUsers[];
        }>("/access/role"),
      ]);

      roles = rolesResponse.roles;
      sourceUsers = usersResponse.users;
      tableRows = getTableRows(usersResponse.users);
      totalCount = usersResponse?.totalCount ?? 0;
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to fetch users");
    } finally {
      isLoading = false;
    }
  }

  async function handleChangingRecordsPerPage(
    event: CustomEvent<{ records: number }>
  ) {
    recordsPerPage = event.detail.records;
    currentPage = 1;
    selectedRows = new Set();
    await loadUsers();
  }

  async function handlePageChange(event: CustomEvent<{ pageNumber: number }>) {
    currentPage = event.detail.pageNumber;
    selectedRows = new Set();
    await loadUsers();
  }

  function handleTextInput() {
    nameInputValue = nameInputValue?.trim();
    debounce(() => {
      handleFilterChange();
    }, 500);
  }

  function handleFilterChange() {
    filters = {
      name: nameInputValue,
    };
    updateUrlWithFilters();
  }

  function updateUrlWithFilters() {
    const parameters = new URLSearchParams();

    if (filters.name) {
      parameters.set("name", filters.name);
    }

    void goto(`${$page.url.pathname}?${parameters.toString()}`, {
      replaceState: true,
      noScroll: true,
    });
  }

  function clearFilters() {
    nameInputValue = "";
    filters = {};
    updateUrlWithFilters();
  }

  async function handleTableRowClick(event: CustomEvent<{ rowId: number }>) {
    const { rowId } = event.detail;
    selectedUser = sourceUsers.find((user) => user.id === rowId);
  }

  async function handleUpdateUserConfirm(
    event: CustomEvent<{
      userId: number;
      addRoleIds: number[];
      removeRoleIds: number[];
    }>
  ) {
    const { userId, addRoleIds, removeRoleIds } = event.detail;

    try {
      await request.post<undefined>(`/users`, {
        userId,
        addRoleIds,
        removeRoleIds,
      });
      await loadUsers();
      successToast("User was updated successfully.");
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to update the user.");
    }
  }

  function handleCloseAside() {
    selectedRows = new Set();
  }
</script>

<main
  id="users-page"
  data-testid="users-page"
  class="grid h-full w-full grid-cols-8 overflow-hidden"
>
  <div
    class="flex flex-col h-full min-h-0"
    class:col-span-5={Boolean(selectedUser)}
    class:col-span-8={!selectedUser}
  >
    <!-- Filter Section -->
    <Filter on:clearFilters={clearFilters} {hasActiveFilter}>
      <div slot="filter-add">
        <div class="flex gap-2 justify-end p-2">
          <Button id="add-new-user" label="Add User">
            <i class="bi bi-plus-circle" slot="icon" />
          </Button>
        </div>
      </div>
      <div
        slot="content"
        class="m-4 flex flex-row content-center justify-between gap-8"
      >
        <TextInput
          id="name-filters"
          label="Name"
          placeholder="Enter User Name"
          classes="flex-1"
          bind:value={nameInputValue}
          on:input={handleTextInput}
        />
      </div>
    </Filter>

    <!-- Table Section with Pagination Footer -->
    <div class="flex flex-col flex-1 overflow-hidden">
      <div class="flex-1 overflow-y-auto scrollbar-hide">
        {#if isLoading}
          <div class="flex h-full items-center justify-center">
            <LoadingSpinner extraClass="w-12 h-12" />
          </div>
        {:else}
          <Table
            {headers}
            rows={tableRows}
            bind:selectedRows
            on:rowSingleClick={handleTableRowClick}
          />
        {/if}
      </div>
      <div class="mt-auto">
        <PaginationFooter
          id="users-table-footer"
          {lastPage}
          {currentPage}
          totalNumberOfItemsListed={totalCount}
          limit={String(recordsPerPage)}
          on:pageChange={handlePageChange}
          on:recordsPerPageChange={handleChangingRecordsPerPage}
        />
      </div>
    </div>
  </div>
  {#if selectedUser}
    <div class="w-full col-span-3">
      <UserAside
        user={selectedUser}
        {roles}
        on:updateUser={handleUpdateUserConfirm}
        on:closeAside={handleCloseAside}
      />
    </div>
  {/if}
</main>
