<script lang="ts">
  type Action = {
    name: string;
    icon: string;
    id: string;
  };

  export let fileName: string;
  export let actions: Action[] = [];
  export let handleOnFileActionClick: (
    filePath: string,
    actionId: string
  ) => void;

  const handleClick = (actionId: string) => {
    if (handleOnFileActionClick) {
      handleOnFileActionClick(fileName, actionId);
    }
  };
</script>

<div class="flex items-center space-x-2 justify-end px-2">
  {#each actions as action}
    <button
      class="flex items-center cursor-pointer px-2 py-1 rounded bg-transparent
             hover:bg-gray-200 focus:outline-none"
      on:click={(event) => {
        event.stopPropagation();
        handleClick(action.id);
      }}
      aria-label={action.name}
      type="button"
    >
      <i class={action.icon}></i>
    </button>
  {/each}
</div>
