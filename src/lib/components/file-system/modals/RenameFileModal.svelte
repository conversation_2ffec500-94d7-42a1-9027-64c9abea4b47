<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import TextInput from "$lib/components/ui/inputs/TextInput.svelte";
  import Modal from "$lib/components/ui/modals/Modal.svelte";

  export let showModal: boolean;
  export let fileName = "";
  export let isFolder = false;

  const dispatch = createEventDispatcher();

  $: newFileName = fileName;
  function handleConfirm() {
    dispatch("confirm", { newFileName });
  }
</script>

<Modal
  title="Rename File: {fileName}"
  id="rename-file-modal"
  bind:showModal
  on:confirm={handleConfirm}
>
  <div slot="content">
    {#if isFolder}
      <p class="text-sm mb-4">
        Renaming this folder will also rename the associated customer folder
        where settlement files are stored, if it is associated.
      </p>
    {/if}

    <TextInput
      label="New File Name"
      bind:value={newFileName}
      placeholder="Enter New File Name"
    />
  </div>
</Modal>
