<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import Modal from "$lib/components//ui/modals/Modal.svelte";
  import TextInput from "$lib/components/ui/inputs/TextInput.svelte";

  export let showModal: boolean;
  export let currentFolder = "";
  let newFolderName = "";

  const dispatch = createEventDispatcher();

  function handleConfirm() {
    newFolderName = newFolderName?.trim();
    dispatch("confirm", { name: newFolderName });
  }
</script>

<Modal
  title="Create Folder in: {currentFolder}"
  id="add-folder-modal"
  bind:showModal
  on:confirm={handleConfirm}
>
  <div slot="content">
    <TextInput
      label="Folder Name"
      bind:value={newFolderName}
      placeholder="Enter Folder Name"
    />
  </div>
</Modal>
