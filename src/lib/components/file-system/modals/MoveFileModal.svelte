<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import FileSystem from "../FileSystem.svelte";

  import Modal from "$lib/components/ui/modals/Modal.svelte";

  export let showModal: boolean;
  export let selectedCount: number;
  export let hasFolderToMove: boolean;
  let currentFolderPath: string;

  const dispatch = createEventDispatcher();

  function handleConfirm() {
    if (currentFolderPath) {
      dispatch("confirm", { destinationPath: currentFolderPath });
    }
  }
</script>

<Modal
  title="Move {selectedCount} {selectedCount === 1 ? 'file' : 'files'}"
  subtitle={hasFolderToMove
    ? `Moving folders will also rename the associated customer folder where
      settlement files are stored, if it is associated.`
    : ""}
  id="move-file-modal"
  bind:showModal
  size="large"
  confirmLabel="Move Here"
  on:confirm={handleConfirm}
>
  <div slot="content">
    <FileSystem isReadOnly={true} bind:currentFolderPath />
  </div>
</Modal>
