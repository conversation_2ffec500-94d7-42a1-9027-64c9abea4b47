import { request } from "$lib/services/api-caller/request";
import { RequestError } from "$lib/utils/errors/request-error";

async function downloadFile(
  folderPath: string,
  fileName: string
): Promise<void> {
  try {
    const response = await request.get<Blob>(
      `/filesystem/download?filepath=${encodeURIComponent(folderPath)}/${fileName}`,
      "blob"
    );
    const url = window.URL.createObjectURL(response);

    const link = document.createElement("a");
    link.href = url;

    link.setAttribute("download", fileName);
    document.body.append(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    if (error instanceof RequestError) {
      throw error;
    }

    const errorMessage =
      error instanceof Error
        ? error.message
        : `Failed to download the file ${fileName}`;
    throw new RequestError(errorMessage, "unknown", undefined, error);
  }
}

export { downloadFile };
