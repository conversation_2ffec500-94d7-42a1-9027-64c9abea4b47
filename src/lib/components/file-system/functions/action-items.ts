import { permissions } from "$lib/constants/access/permissions";
import { resources } from "$lib/constants/access/resources";
import { checkPermission } from "$lib/utils/check-permission";

const fileActionItems = [
  {
    id: "download",
    tooltip: "Download",
    icon: "bi bi-download",
    shortcut: true,
  },
  {
    id: "move",
    tooltip: "Move",
    icon: "bi bi-folder-symlink",
    shortcut: false,
  },
  {
    id: "delete",
    tooltip: "Delete",
    icon: "bi bi-trash",
    classes: "text-red-500 hover:text-red-700",
    shortcut: false,
  },
];

const folderActionItems = [
  { id: "rename", tooltip: "Rename", icon: "bi bi-pencil", shortcut: true },
  {
    id: "move",
    tooltip: "Move",
    icon: "bi bi-folder-symlink",
    shortcut: false,
  },
  {
    id: "delete",
    tooltip: "Delete",
    icon: "bi bi-trash",
    classes: "text-red-500 hover:text-red-700",
    shortcut: false,
  },
];

function getPermittedFileActions(): typeof fileActionItems {
  return fileActionItems.filter((item) => {
    if (item.id === "rename" || item.id === "move") {
      return checkPermission(resources.filesystem, permissions.update);
    }

    if (item.id === "download") {
      return checkPermission(resources.filesystem, permissions.read);
    }

    if (item.id === "delete") {
      return checkPermission(resources.filesystem, permissions.delete);
    }

    return false;
  });
}

function getPermittedFolderActions(): typeof folderActionItems {
  return folderActionItems.filter((item) => {
    if (item.id === "rename" || item.id === "move") {
      return checkPermission(resources.filesystem, permissions.update);
    }

    if (item.id === "delete") {
      return checkPermission(resources.filesystem, permissions.delete);
    }

    return false;
  });
}

export {
  fileActionItems,
  folderActionItems,
  getPermittedFileActions,
  getPermittedFolderActions,
};
