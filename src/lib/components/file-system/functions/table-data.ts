import { fileActionItems, folderActionItems } from "./action-items";
import { formatDate } from "../../../utils/date-utils";
import ActionCell from "../table-cells/ActionCell.svelte";

import IconOrText from "$lib/components/ui/table/common-cell-components/IconOrText.svelte";
import { type Row, type Header } from "$lib/components/ui/table/types";

import type { FileItem } from "./types";

const getTableRows = (
  rows: FileItem[],
  isReadOnly: boolean,
  handleOnFileActionClick: (fileName: string, actionId: string) => void
): Row[] => {
  const tableRows: Row[] = [];

  for (const [, item] of rows.entries()) {
    const newRow: Row = {
      id: item.name,
      showCheckbox: !isReadOnly,
      cells: [
        {
          component: IconOrText,
          props: {
            left: {
              text:
                item.type === "file"
                  ? "text-blue-500 bi bi-file-fill"
                  : "bi bi-folder-fill",
              isIcon: true,
            },
          },
        },
        item.name,
        formatDate(new Date(item.modified)),
        item?.size?.toString() ?? "-",
        isReadOnly
          ? ""
          : {
              component: ActionCell,
              props: {
                actions:
                  item.type === "file"
                    ? fileActionItems.filter((action) => action.shortcut)
                    : folderActionItems.filter((action) => action.shortcut),
                fileName: item.name,
                handleOnFileActionClick,
              },
            },
      ],
    };
    tableRows.push(newRow);
  }

  return tableRows;
};

const getTableHeaders = (isReadOnly: boolean): Header[] => {
  const headers: Header[] = [
    { id: "icon", title: "", fixedWidth: true },
    { id: "name", title: "Name", sortable: true },
    { id: "modified", title: "Date Modified", sortable: true },
    { id: "size", title: "Size(kb)", sortable: true },
  ];

  // If isReadOnly is false, add the "Actions" header
  if (!isReadOnly) {
    headers.push({
      id: "action",
      title: "Actions",
      align: "right",
      fixedWidth: true,
    });
  }

  return headers;
};

export { getTableRows, getTableHeaders };
