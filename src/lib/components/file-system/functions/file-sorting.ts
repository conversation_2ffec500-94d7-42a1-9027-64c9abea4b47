import type { FileItem } from "./types";

const sortByName = (
  items: FileItem[],
  direction: "asc" | "desc" | "none"
): FileItem[] => {
  const sortedItems = items.sort((a, b) => {
    if (direction === "none") {
      return 0;
    }

    // First, group folders before files
    if (a.type === "folder" && b.type === "file") {
      return -1;
    }

    if (a.type === "file" && b.type === "folder") {
      return 1;
    }

    // Then sort alphabetically by name
    const comparison = a.name.localeCompare(b.name);

    return direction === "asc" ? comparison : -comparison;
  });

  return sortedItems;
};

const sortByDate = (
  items: FileItem[],
  direction: "asc" | "desc" | "none"
): FileItem[] => {
  const sortedItems = items.sort((a, b) => {
    if (direction === "none") {
      return 0;
    }

    // Group folders before files
    if (a.type === "folder" && b.type === "file") {
      return -1;
    }

    if (a.type === "file" && b.type === "folder") {
      return 1;
    }

    // Compare dates
    const dateA = new Date(a.modified).getTime();
    const dateB = new Date(b.modified).getTime();

    // Compare the two dates
    const comparison = dateA - dateB;

    return direction === "asc" ? comparison : -comparison;
  });

  return sortedItems;
};

const sortBySize = (
  items: FileItem[],
  direction: "asc" | "desc" | "none"
): FileItem[] => {
  const sortedItems = items.sort((a, b) => {
    if (direction === "none") {
      return 0;
    }

    // Group folders before files
    if (a.type === "folder" && b.type === "file") {
      return -1;
    }

    if (a.type === "file" && b.type === "folder") {
      return 1;
    }

    // Compare sizes, treating undefined sizes as 0
    const sizeA = a.size ?? 0;
    const sizeB = b.size ?? 0;

    // Compare the two sizes
    const comparison = sizeA - sizeB;

    return direction === "asc" ? comparison : -comparison;
  });

  return sortedItems;
};

export { sortByName, sortByDate, sortBySize };
