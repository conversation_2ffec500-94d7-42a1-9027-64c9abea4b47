<script lang="ts">
  import { onMount } from "svelte";

  import FileSystemHeader from "./fragments/FileSystemHeader.svelte";
  import { fileActionItems, folderActionItems } from "./functions/action-items";
  import { downloadFile } from "./functions/download-file";
  import { sortByName, sortByDate, sortBySize } from "./functions/file-sorting";
  import { getTableHeaders, getTableRows } from "./functions/table-data";
  import DeleteFileModal from "./modals/DeleteFileModal.svelte";
  import DownloadFileModal from "./modals/DownloadFileModal.svelte";
  import MoveFileModal from "./modals/MoveFileModal.svelte";
  import RenameFileModal from "./modals/RenameFileModal.svelte";
  import Table from "../ui/table/Table.svelte";

  import { goto } from "$app/navigation";
  import { page } from "$app/stores";
  import { type FileItem } from "$lib/components/file-system/functions/types";
  import {
    failureToast,
    successToast,
  } from "$lib/components/ui/notifications/toast";
  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";
  import { type Row, type OverlayAction } from "$lib/components/ui/table/types";
  import { request } from "$lib/services/api-caller/request";
  import { type RequestError } from "$lib/utils/errors/request-error";

  export let id = "id-file-system";
  export let isReadOnly = false;
  export let currentFolderPath = "/";

  const headers = getTableHeaders(isReadOnly);
  let currentFiles: FileItem[] = [];

  let tableRows: Row[] = [];
  let selectedRows = new Set<string>();
  let searchValue = "";
  let overlayActions: OverlayAction[] = [];
  let isDeleteModalOpen = false;
  let isDownloadModalOpen = false;
  let isRenameModalOpen = false;
  let isMoveModalOpen = false;
  let isLoading = false;

  $: overlayActions = [];
  $: tableRows = getTableRows(
    filterFiles(currentFiles, searchValue),
    isReadOnly,
    handleOnFileActionClick
  );

  $: breadcrumbs = [
    { name: "Home", path: "/" },
    ...currentFolderPath
      .split("/")
      .filter(Boolean)
      .map((part, index, array) => ({
        name: part,
        path: "/" + array.slice(0, index + 1).join("/"),
      })),
  ];

  $: if (currentFolderPath) {
    searchValue = "";
  }

  $: {
    const path = sanitizePath($page.url.searchParams.get("filepath") ?? "/");

    if (!isReadOnly && path !== currentFolderPath) {
      // eslint-disable-next-line unicorn/prefer-top-level-await
      void fetchFiles(path);
    }
  }

  onMount(async () => {
    const path = sanitizePath($page.url.searchParams.get("filepath") ?? "/");
    await fetchFiles(path);
  });

  async function fetchFiles(path: string): Promise<void> {
    try {
      isLoading = true;
      const response = await request.get<FileItem[]>(
        `/filesystem?filepath=${encodeURIComponent(path)}`
      );
      currentFiles = response ?? [];
      currentFolderPath = path;
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(
        requestError?.message ?? "Failed to fetch monthly settlements"
      );
    } finally {
      isLoading = false;
    }
  }

  function filterFiles(files: FileItem[], searchName: string): FileItem[] {
    return files.filter((item) =>
      item.name.toLowerCase().includes(searchName.trim().toLowerCase())
    );
  }

  function handleRowDoubleClick(event: CustomEvent<{ rowId: string }>): void {
    const { rowId } = event.detail;
    const selectedFile = currentFiles.find((file) => file.name === rowId);

    if (selectedFile?.type === "folder") {
      const newPath =
        currentFolderPath === "/"
          ? `/${rowId}`
          : `${currentFolderPath}/${rowId}`;

      if (isReadOnly) {
        void fetchFiles(newPath);
      } else {
        void goto(`/file-system?filepath=${encodeURIComponent(newPath)}`);
      }
    }
  }

  function handleBreadcrumbClick(
    event: CustomEvent<{ filePath: string }>
  ): void {
    const { filePath } = event.detail;

    if (isReadOnly) {
      void fetchFiles(filePath);
    } else {
      void goto(`/file-system?filepath=${encodeURIComponent(filePath)}`);
    }
  }

  function handleSelectionChange(
    event: CustomEvent<{ selectedRowIds: string }>
  ): void {
    const { selectedRowIds } = event.detail;

    if (selectedRowIds.length === 1) {
      const selectedFile = currentFiles.find(
        (file) => file.name === selectedRowIds[0]
      );

      if (selectedFile && !isReadOnly) {
        overlayActions =
          selectedFile.type === "file" ? fileActionItems : folderActionItems;
      }
    } else if (selectedRowIds.length > 1) {
      // Only move is allowed for multiple files selection
      overlayActions = folderActionItems.filter((item) => item.id === "move");
    } else {
      overlayActions = [];
    }
  }

  function handleOnFileActionClick(fileName: string, actionId: string): void {
    switch (actionId) {
      case "rename": {
        selectedRows = new Set([fileName]);
        isRenameModalOpen = true;
        break;
      }

      case "delete": {
        selectedRows = new Set([fileName]);
        isDeleteModalOpen = true;
        break;
      }

      case "move": {
        isMoveModalOpen = true;
        break;
      }

      case "download": {
        selectedRows = new Set([fileName]);
        isDownloadModalOpen = true;
        break;
      }

      default: {
        // Do nothing
        break;
      }
    }
  }

  async function handleOverlayActionClick(
    event: CustomEvent<{ actionId: string }>
  ) {
    const { actionId } = event.detail;

    for (const selectedRowId of selectedRows) {
      handleOnFileActionClick(selectedRowId, actionId);
    }
  }

  function handleSortChange(
    event: CustomEvent<{
      id: string;
      direction: "asc" | "desc" | "none";
    }>
  ) {
    const { id, direction } = event.detail;
    let sorted: FileItem[] = [];

    switch (id) {
      case "name": {
        sorted = sortByName(filterFiles(currentFiles, searchValue), direction);

        break;
      }

      case "modified": {
        sorted = sortByDate(filterFiles(currentFiles, searchValue), direction);

        break;
      }

      case "size": {
        sorted = sortBySize(filterFiles(currentFiles, searchValue), direction);

        break;
      }

      default: {
        // Do nothing
        break;
      }
    }

    if (sorted.length > 0) {
      tableRows = getTableRows(sorted, isReadOnly, handleOnFileActionClick);
    }
  }

  async function handleCreateFolderConfirm(
    event: CustomEvent<{ name: string }>
  ) {
    const { name } = event.detail;

    if (!name) {
      failureToast("Folder name cannot be empty.");

      return;
    }

    try {
      isLoading = true;
      await request.post<undefined>(`/filesystem/folder`, {
        parentPath: currentFolderPath,
        name,
      });
      successToast("Folder was created successfully.");
      await fetchFiles(currentFolderPath);
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to create folder");
    } finally {
      isLoading = false;
    }
  }
  async function handleDeleteConfirm() {
    isDeleteModalOpen = false;
    const selectedFile = selectedRows.values().next().value as string;

    try {
      isLoading = true;
      await request.delete<undefined>(`/filesystem`, {
        filePath: `${currentFolderPath}/${selectedFile}`,
      });
      successToast("File was delete successfully");
      await fetchFiles(currentFolderPath);
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to delete");
    } finally {
      isLoading = false;
    }
  }
  async function handleDownloadConfirm() {
    isDownloadModalOpen = false;
    const selectedFile = selectedRows.values().next().value as string;

    try {
      await downloadFile(currentFolderPath, selectedFile);
      successToast("File successfully downloaded");
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to download");
    }
  }
  async function handleRenameConfirm(
    event: CustomEvent<{ newFileName: string }>
  ) {
    const { newFileName } = event.detail;
    isRenameModalOpen = false;
    const selectedFile = selectedRows.values().next().value as string;

    if (!newFileName) {
      failureToast("File name cannot be empty");

      return;
    }

    try {
      isLoading = true;
      await request.post<undefined>(`/filesystem/rename`, {
        filePath: `${currentFolderPath.replace(/\/$/, "")}/${selectedFile}`,
        newName: newFileName.trim(),
      });
      successToast("File was renamed successfully");
      await fetchFiles(currentFolderPath);
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to rename");
    } finally {
      isLoading = false;
    }
  }
  async function handleMoveConfirm(
    event: CustomEvent<{ destinationPath: string }>
  ) {
    const { destinationPath } = event.detail;
    isMoveModalOpen = false;
    const selectedFiles = [...selectedRows.values()];
    const sourcePaths = selectedFiles.map(
      (file) => `${currentFolderPath.replace(/\/$/, "")}/${file}`
    );

    try {
      isLoading = true;
      await request.post<undefined>(`/filesystem/move`, {
        sourcePaths,
        destinationPath,
      });
      successToast("Files were moved successfully");
      await fetchFiles(currentFolderPath);
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? "Failed to move");
    } finally {
      isLoading = false;
    }
  }

  function sanitizePath(path: string): string {
    // Start with a single `/` and avoid multiple consecutive slashes.
    return "/" + path.replaceAll(/\/+/g, "/").replace(/^\/?/, "");
  }
</script>

<div {id} class="flex flex-col h-full">
  <FileSystemHeader
    {breadcrumbs}
    on:breadcrumbClick={handleBreadcrumbClick}
    on:addFolder={handleCreateFolderConfirm}
    bind:searchValue
  />
  <div class="flex-1 overflow-y-auto scrollbar-hide">
    {#if isLoading}
      <div class="flex flex-grow h-full items-center justify-center">
        <LoadingSpinner extraClass="w-12 h-12" />
      </div>
    {:else}
      <div class="flex flex-col flex-grow">
        <Table
          {headers}
          rows={tableRows}
          {...isReadOnly ? undefined : { overlayActions }}
          bind:selectedRows
          on:sortChange={handleSortChange}
          on:rowDoubleClick={handleRowDoubleClick}
          on:selectionChange={handleSelectionChange}
          on:overlayAction={handleOverlayActionClick}
        />
      </div>
    {/if}
  </div>
  <DeleteFileModal
    bind:showModal={isDeleteModalOpen}
    fileName={selectedRows.values().next().value}
    on:confirm={handleDeleteConfirm}
  />
  {#if isMoveModalOpen}
    <MoveFileModal
      bind:showModal={isMoveModalOpen}
      selectedCount={selectedRows.size}
      hasFolderToMove={currentFiles.find(
        (file) => file.name === selectedRows.values().next().value
      )?.type === "folder"}
      on:confirm={handleMoveConfirm}
    />
  {/if}
  {#if isRenameModalOpen}
    <RenameFileModal
      bind:showModal={isRenameModalOpen}
      fileName={selectedRows.values().next().value}
      isFolder={currentFiles.find(
        (file) => file.name === selectedRows.values().next().value
      )?.type === "folder"}
      on:confirm={handleRenameConfirm}
    />
  {/if}
  <DownloadFileModal
    bind:showModal={isDownloadModalOpen}
    fileName={selectedRows.values().next().value}
    on:confirm={handleDownloadConfirm}
  />
</div>
