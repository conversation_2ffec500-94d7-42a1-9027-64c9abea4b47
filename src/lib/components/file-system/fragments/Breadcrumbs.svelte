<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import CopyButton from "$lib/components/ui/buttons/CopyButton.svelte";

  export let breadcrumbs: Array<{ name: string; path: string }> = [];

  const dispatch = createEventDispatcher();

  $: lastBreadcrumbPath = breadcrumbs.at(-1)?.path ?? "";

  function handleClick(filePath: string): void {
    dispatch("breadcrumbClick", { filePath });
  }
</script>

<div class="flex items-center space-x-2">
  <div
    class="flex items-center flex-grow space-x-4 overflow-x-auto scrollbar-hide"
  >
    {#each breadcrumbs as breadcrumb, index}
      {#if index !== 0}
        <!-- Add "/" only for items after the first breadcrumb -->
        <p class="text-gray-600">/</p>
      {/if}

      <button
        tabindex="0"
        class="text-gray-600 cursor-pointer"
        on:click|preventDefault={() => {
          handleClick(breadcrumb.path);
        }}
      >
        {#if index === 0 || index === breadcrumbs.length - 1}
          <!-- Show the first and last breadcrumb -->
          <p class="text-blue-500">{breadcrumb.name}</p>
        {:else}
          ...
        {/if}
      </button>
    {/each}
  </div>
  <CopyButton text={lastBreadcrumbPath} />
</div>
