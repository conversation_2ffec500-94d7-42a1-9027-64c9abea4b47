<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import Breadcrumbs from "./Breadcrumbs.svelte";
  import Button from "../../ui/buttons/Button.svelte";
  import SearchBarInput from "../../ui/inputs/SearchBarInput.svelte";
  import AddFolderModal from "../modals/AddFolderModal.svelte";

  export let hideAddFolder = false;
  export let searchValue = "";
  export let breadcrumbs: Array<{ name: string; path: string }> = [];

  let isAddModalOpen = false;

  const dispatch = createEventDispatcher();

  // Get the current folder (last breadcrumb)
  $: currentFolder =
    breadcrumbs.length > 0 && breadcrumbs.at(-1)
      ? breadcrumbs.at(-1)!.name
      : "";

  function handleAddModalConfirmClick(event: CustomEvent<{ name: string }>) {
    const { name } = event.detail;
    isAddModalOpen = false;
    dispatch("addFolder", { name });
  }
</script>

<div class="flex justify-center items-center border-y border-gray-200">
  <div class="px-4 py-4 w-3/5">
    <Breadcrumbs {breadcrumbs} on:breadcrumbClick />
  </div>
  <div class="flex w-2/5 px-2 border-l border-gray-200 justify-between">
    <div class={hideAddFolder ? "w-full" : "w-3/4"}>
      <SearchBarInput
        placeholder={`Search in ${currentFolder}`}
        bind:value={searchValue}
        on:click
      />
    </div>
    {#if !hideAddFolder}
      <div>
        <Button
          label="Add Folder"
          on:click={() => {
            isAddModalOpen = true;
          }}
        />
      </div>
    {/if}
  </div>
  {#if isAddModalOpen}
    <AddFolderModal
      bind:showModal={isAddModalOpen}
      {currentFolder}
      on:confirm={handleAddModalConfirmClick}
    />
  {/if}
</div>
