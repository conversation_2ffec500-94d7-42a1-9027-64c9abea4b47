<script lang="ts">
  import Button from "../ui/buttons/Button.svelte";
  import PasswordInput from "../ui/inputs/PasswordInput.svelte";
  import InputText from "../ui/inputs/TextInput.svelte";
  import { failureToast, successToast } from "../ui/notifications/toast";

  import { goto } from "$app/navigation";
  import { request } from "$lib/services/api-caller/request";
  import { authStore } from "$lib/stores/auth-store";
  import { type UserWithRoles } from "$lib/types/user-profile";
  import { type RequestError } from "$lib/utils/errors/request-error";

  let username = "";
  let password = "";
  let isLoading = false;

  const handleSubmit = async () => {
    try {
      isLoading = true;
      const response = await request.post<{ userProfile: UserWithRoles }>(
        "/session",
        {
          username,
          password,
        }
      );

      authStore.set({
        isLoggedIn: true,
        userProfile: response.userProfile,
      });
      successToast("Logged in successfully!");
      await goto("/");
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(
        requestError?.message ??
          "We are having trouble logging you in. Please try again shortly."
      );
    } finally {
      isLoading = false;
    }
  };
</script>

<div class="mt-10">
  <div>
    <form
      class="space-y-6"
      data-testid="form"
      on:submit|preventDefault={handleSubmit}
    >
      <div>
        <div class="mt-2">
          <InputText
            id="username"
            label={"Username"}
            bind:value={username}
            isRequired={true}
          />
        </div>
      </div>

      <div>
        <div class="flex justify-between">
          <label
            for="password"
            class="block text-sm font-medium leading-6 text-gray-900"
            >Password</label
          >
        </div>

        <div class="mt-2">
          <div class="relative">
            <PasswordInput id="password" bind:password isRequired={true} />
          </div>
        </div>
      </div>
      <div class="flex w-full">
        <Button
          type="submit"
          id="submit"
          color="blue"
          label="Sign in"
          {isLoading}
        />
      </div>
    </form>
  </div>
</div>
