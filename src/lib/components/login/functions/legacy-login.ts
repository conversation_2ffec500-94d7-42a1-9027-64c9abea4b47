import { request } from "$lib/services/api-caller/request";
import { authStore } from "$lib/stores/auth-store";
import { type RequestError } from "$lib/utils/errors/request-error";

import type { UserWithRoles } from "$lib/types/user-profile";

const legacyLogin = async (
  token: string
): Promise<{
  isSuccess: boolean;
  message: string;
}> => {
  try {
    const response = await request.post<{ userProfile: UserWithRoles }>(
      "/session/legacy",
      {
        token,
      }
    );
    authStore.set({
      isLoggedIn: true,
      userProfile: response.userProfile,
    });

    return {
      isSuccess: true,
      message: "Legacy Auth check successfull",
    };
  } catch (error) {
    const requestError = error as RequestError;

    return {
      isSuccess: false,
      message:
        requestError?.message ?? "We are having trouble authenticating you.",
    };
  }
};

export { legacyLogin };
