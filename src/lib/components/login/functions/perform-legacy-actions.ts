import { legacyLogin } from "./legacy-login";

import { request } from "$lib/services/api-caller/request";

import type { UserWithRoles } from "$lib/types/user-profile";

const performLegacyLoginAndActions = async (
  token: string,
  pathName: string,
  queries: Record<string, string>
): Promise<boolean> => {
  const { isSuccess } = await legacyLogin(token);

  if (isSuccess && pathName === "/email") {
    const { transferInId } = queries;

    await request.post<UserWithRoles>("/email/wire-transfer", { transferInId });
  }

  return isSuccess;
};

export { performLegacyLoginAndActions };
