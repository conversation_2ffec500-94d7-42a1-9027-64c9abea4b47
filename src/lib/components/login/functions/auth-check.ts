import { request } from "$lib/services/api-caller/request";
import { authStore } from "$lib/stores/auth-store";
import { type UserWithRoles } from "$lib/types/user-profile";
import { type RequestError } from "$lib/utils/errors/request-error";

const authCheck = async (): Promise<{
  isSuccess: boolean;
  message: string;
}> => {
  try {
    const response = await request.get<{ userProfile: UserWithRoles }>(
      "/session"
    );

    authStore.set({
      isLoggedIn: true,
      userProfile: response.userProfile,
    });

    return {
      isSuccess: true,
      message: "Auth check successfull",
    };
  } catch (error) {
    const requestError = error as RequestError;

    return {
      isSuccess: false,
      message:
        requestError?.message ?? "We are having trouble authenticating you.",
    };
  }
};

export { authCheck };
