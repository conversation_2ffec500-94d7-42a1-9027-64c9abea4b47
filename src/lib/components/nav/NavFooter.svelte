<script lang="ts">
  import { goto } from "$app/navigation";
  import { PUBLIC_ENVIRONMENT } from "$env/static/public";
  import Button from "$lib/components/ui/buttons/ButtonNew.svelte";

  export let collapsed = false;
  export let hideMobileDropdown = true;
  export let id = "";
  export let isDark = document.documentElement.classList.contains("dark");

  // Dark Mode Toggle
  function toggle() {
    document.documentElement.classList.toggle("dark");
    isDark = !isDark;
  }

  let buttonSize = "small" as "small" | "medium";
  $: buttonSize = collapsed ? "medium" : "small";

  const enableDarkModeLightModeToggle = PUBLIC_ENVIRONMENT !== "production";
</script>

<div
  class="mt-auto w-full border-t border-gray-300 dark:border-neutral-600 max-lg:border-0"
>
  <button
    id="nav{id}-collapse-button"
    data-testid="nav{id}-collapse-button"
    on:click={() => {
      collapsed = !collapsed;
    }}
    class="flex w-full items-center justify-center py-4 hover:bg-gray-100
    dark:hover:bg-neutral-800 max-lg:hidden"
  >
    {#if collapsed}
      <i
        class="bi bi-chevron-double-right text-gray-800 hover:text-gray-600
      dark:text-white dark:hover:text-neutral-300"
      />
    {:else}
      <i
        class="bi bi-chevron-double-left text-gray-800 hover:text-gray-600
      dark:text-white dark:hover:text-neutral-300"
      />
    {/if}
  </button>

  <div
    class="flex h-16 flex-row items-center justify-center gap-2 border-t
    dark:border-neutral-600 p-4"
    class:flex-col-reverse={collapsed}
    class:h-auto={collapsed}
  >
    <Button
      color="red"
      intent="outline"
      size={buttonSize}
      href="/logout"
      className="text-md"
      on:click={async () => {
        hideMobileDropdown = true;
        await goto("/logout");
      }}
    >
      {#if collapsed}
        <i class="bi bi-box-arrow-left text-red-500 hover:text-red-600" />
      {:else}
        Sign Out
      {/if}
    </Button>

    <Button
      id="nav{id}-profile-button"
      data-testid="nav{id}-profile-button"
      intent="outline"
      color="dark"
      size={buttonSize}
      href="/profile"
      className="text-md"
      on:click={async () => {
        hideMobileDropdown = true;
        await goto("/profile");
      }}
    >
      {#if collapsed}
        <i class="bi bi-person" />
      {:else}
        Account
      {/if}
    </Button>

    {#if enableDarkModeLightModeToggle}
      <Button
        color="dark"
        intent="outline"
        size={buttonSize}
        className="text-md"
        on:click={() => {
          toggle();
        }}
      >
        {#if isDark}
          <i class="bi bi-sun"></i>
        {:else}
          <i class="bi bi-moon"></i>
        {/if}
      </Button>
    {/if}
  </div>
</div>
