<script lang="ts">
  import NavFooter from "./NavFooter.svelte";
  import NavGroup from "./NavGroup.svelte";
  import squareLogo from "../../assets/square-logo.png";
  import { tabs } from "../../constants/theme/tabs";

  let hideMobileDropdown = true;
  export let pageRoute: string;
</script>

<div class="fixed top-0 z-50 w-full bg-white dark:bg-neutral-900">
  <div
    class=" flex h-16 flex-row items-center justify-between border-b border-gray-300
    dark:border-neutral-600 px-4 lg:hidden"
    data-testid="nav-mobile-header"
  >
    <div class="my-5 flex shrink-0 items-center justify-center">
      <img
        data-testid="nav-mobile-logo"
        id="nav-mobile-logo"
        class="h-6 w-auto"
        src={squareLogo}
        alt={"FASquareLogo"}
      />
    </div>
    <button
      class=" h-min items-center rounded-md bg-white dark:bg-neutral-800 p-2
      text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300
      dark:ring-neutral-600 hover:bg-gray-100"
      data-testid="nav-mobile-expand-button"
      id="nav-mobile-expand-button"
      on:click={() => {
        hideMobileDropdown = !hideMobileDropdown;
      }}
    >
      <i class="bi bi-list dark:text-white dark:hover:text-gray-300" />
    </button>
  </div>

  <!--Dropdown for Mobile-->
  <div
    class="flex h-screen w-full flex-col overflow-auto bg-white dark:bg-neutral-900 lg:hidden"
    class:hidden={hideMobileDropdown}
  >
    <nav
      id="nav-mobile-element"
      data-testid="nav-mobile-element"
      class="scrollbar min-h-fit overflow-auto p-4 pb-36"
    >
      <ul>
        <li>
          <ul>
            {#each tabs as group}
              <NavGroup
                bind:hideMobileDropdown
                label={group.name}
                {pageRoute}
                links={group.links}
              />
            {/each}
          </ul>
        </li>
      </ul>
    </nav>

    <div class="fixed bottom-0 w-full bg-white dark:bg-neutral-900">
      <NavFooter id="-mobile" bind:hideMobileDropdown />
    </div>
  </div>
</div>
