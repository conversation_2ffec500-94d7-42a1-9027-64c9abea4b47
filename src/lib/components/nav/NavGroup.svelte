<script lang="ts">
  import NavLink from "$lib/components/nav/NavLink.svelte";
  import { authStore } from "$lib/stores/auth-store";
  import { checkPermission } from "$lib/utils/check-permission";

  import type { Permission } from "$lib/constants/access/permissions";
  import type { Resource } from "$lib/constants/access/resources";

  export let label: string;
  export let pageRoute: string;
  export let collapsed = false;
  export let hideMobileDropdown = true;
  const slug = label.toLowerCase().replaceAll(/[ /]/g, "-");

  export let links: Array<{
    name: string;
    icon: string;
    resource: Resource;
    permission: Permission;
  }>;
  let isDisplayed = false;
  $: auth = $authStore;
  $: {
    if (auth.isLoggedIn) {
      for (const link of links) {
        if (link.resource) {
          isDisplayed = checkPermission(link.resource, link.permission);
        }

        /**
         * Groups are hidden by default, and their visibility controlled by `isDisplayed`. The way
         * we determine if a group should be displayed is by checking if at least one link in that
         * group is visible to the user. If this is the case then `isDisplayed` is set to
         * true. Once `isDisplayed` is true, it should not be set to false again, thus there is no
         * need to continue checking the rest of the links in the group.
         */
        if (isDisplayed) {
          break;
        }
      }
    }
  }
</script>

{#if isDisplayed}
  <li class="mb-4">
    <div
      class="block rounded-md py-2 pb-0 text-xs font-medium text-primary-500"
      class:ml-2={!collapsed}
      data-testid={"group-label-" + slug}
    >
      {#if !collapsed}
        {label}
      {:else}
        <hr
          class="h-px w-full border-0 bg-gray-200 dark:bg-neutral-600"
          data-testid={"group-hr-" + slug}
        />
      {/if}
    </div>

    {#each links as link}
      <NavLink
        on:click={() => {
          // On click hide the mobile dropdown
          hideMobileDropdown = true;
        }}
        {collapsed}
        label={link.name}
        icon={link.icon}
        {pageRoute}
        {link}
      />
    {/each}
  </li>
{/if}
