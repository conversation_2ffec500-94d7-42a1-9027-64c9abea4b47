<script lang="ts">
  import NavFooter from "./NavFooter.svelte";
  import NavGroup from "./NavGroup.svelte";
  import MobileNav from "./NavMobile.svelte";
  import horizontalLogo from "../../assets/horizontal-logo.png";
  import lightHorizontalLogo from "../../assets/light-horizontal-logo.png";
  import squareLogo from "../../assets/square-logo.png";

  import { tabs } from "$lib/constants/theme/tabs";

  let innerWidth = 0;
  let isDark = document.documentElement.classList.contains("dark");

  let collapsed = true;
  // Never collapse on mobile
  $: collapsed = innerWidth < 1024;
  export let pageRoute: string;
</script>

<svelte:window bind:innerWidth />

{#if innerWidth > 1024}
  <div
    class="flex h-full w-20 flex-col justify-between border-r border-gray-300
    dark:border-neutral-600 bg-white max-lg:hidden dark:bg-neutral-900"
    class:w-56={!collapsed}
  >
    <div class="my-5 flex shrink-0 items-center justify-center">
      <img
        data-testid="nav-logo"
        id="nav-logo"
        class="h-[35px] w-auto"
        class:h-8={collapsed}
        src={collapsed
          ? squareLogo
          : isDark
            ? lightHorizontalLogo
            : horizontalLogo}
        alt={collapsed ? "squareLogo" : "horizontalLogo"}
      />
    </div>

    <nav
      id="nav-element"
      data-testid="nav-element"
      class=" scrollbar w-full overflow-y-auto px-4"
    >
      <ul>
        <li>
          <ul>
            {#each tabs as group}
              <NavGroup
                {collapsed}
                label={group.name}
                {pageRoute}
                links={group.links}
              />
            {/each}
          </ul>
        </li>
      </ul>
    </nav>
    <NavFooter bind:collapsed bind:isDark />
  </div>
{:else}
  <MobileNav {pageRoute} />
{/if}
