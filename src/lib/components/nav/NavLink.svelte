<script lang="ts">
  import { type Permission } from "$lib/constants/access/permissions";
  import { authStore } from "$lib/stores/auth-store";
  import { checkPermission } from "$lib/utils/check-permission";

  import type { Resource } from "$lib/constants/access/resources";

  export let pageRoute: string;
  export let link: {
    name: string;
    icon: string;
    resource: Resource;
    permission: Permission;
  };
  export let label: string;
  export let icon: string;

  const slug = label.toLowerCase().replaceAll(/[ /]/g, "-");
  const currentRoute = `/${slug}`;

  export let collapsed = false;
  $: isActive = pageRoute.includes(currentRoute);
  let isDisplayed = false;

  $: {
    if ($authStore.isLoggedIn) {
      isDisplayed = checkPermission(link.resource, link.permission);
    }
  }
</script>

{#if isDisplayed}
  <li>
    <a
      class="text-md my-2 flex flex-row gap-2 rounded-md px-2 py-3 font-normal"
      class:justify-center={collapsed}
      class:bg-blue-600={isActive}
      class:text-white={isActive}
      class:text-gray-800={!isActive}
      class:dark:text-white={!isActive}
      class:hover:bg-gray-100={!isActive}
      class:dark:hover:bg-neutral-800={!isActive}
      class:hover:text-gray-600={!isActive}
      data-testid="link-{slug}"
      href={currentRoute}
      on:click
    >
      <div>
        <i class={icon} id="link-{slug}-icon" data-testid="link-{slug}-icon" />
      </div>

      {#if !collapsed}
        {label}
      {/if}
    </a>
  </li>
{/if}
