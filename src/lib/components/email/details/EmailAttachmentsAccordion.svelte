<script lang="ts">
  import { slide } from "svelte/transition";

  import { downloadFile } from "$lib/components/file-system/functions/download-file";
  import Modal from "$lib/components/ui/modals/Modal.svelte";
  import {
    failureToast,
    successToast,
  } from "$lib/components/ui/notifications/toast";
  import { type RequestError } from "$lib/utils/errors/request-error";

  export let attachments: Array<{ filePath: string; isFileExist: boolean }>;

  let attachmentsVisible = true;
  let isDownloadModalOpen = false;
  let selectedFile = "";

  function toggleAccordion() {
    attachmentsVisible = !attachmentsVisible;
  }
  async function handleFileDownload(filePath: string) {
    const fileName = getFileName(filePath);

    try {
      isDownloadModalOpen = false;
      await downloadFile(
        filePath.slice(0, Math.max(0, filePath.lastIndexOf("/"))),
        fileName
      );
      successToast(`File ${fileName} was downloaded successfully`);
    } catch (error) {
      const requestError = error as RequestError;
      failureToast(requestError?.message ?? `File ${fileName} download failed`);
    }
  }

  function getFileName(filePath: string): string {
    return filePath?.includes("/")
      ? (filePath.split("/").pop() ?? "")
      : filePath || "";
  }
</script>

<!-- Attachments Accordion -->
<div class="flex flex-col">
  {#if attachments?.length > 0}
    <div class="bg-white border border-gray-300 rounded-md">
      <button
        class="relative cursor-pointer items-center justify-center p-[12px] w-full
        text-left {attachmentsVisible && 'border-b-[1px]'}"
        aria-expanded={attachmentsVisible}
        on:click={toggleAccordion}
        on:keydown={(event) => {
          if (event.key === "Enter" || event.key === " ") {
            event.preventDefault();
            toggleAccordion();
          }
        }}
      >
        <label
          for="attachmentsField"
          class="text-md text-gray-800 cursor-pointer"
        >
          Attachments
        </label>
        <span
          class="absolute right-0 top-0 h-full flex items-center justify-center w-11 cursor-pointer"
          aria-label="Toggle Attachments"
        >
          {#if attachmentsVisible}
            <i class="bi bi-chevron-down" />
          {:else}
            <i class="bi bi-chevron-right" />
          {/if}
        </span>
      </button>
      {#if attachmentsVisible}
        <div
          transition:slide={{ duration: 300 }}
          class="grid grid-cols-1 md:grid-cols-2 my-4 mx-2"
        >
          {#each attachments as attachmentItem}
            <button
              class="text-center border border-gray-300 border-rounded mx-2 p-2 gap-0 cursor-pointer
              hover:bg-gray-100"
              class:border-red-300={!attachmentItem.isFileExist}
              class:border-gray-300={attachmentItem.isFileExist}
              on:click={() => {
                if (attachmentItem.isFileExist) {
                  selectedFile = attachmentItem.filePath;
                  isDownloadModalOpen = !isDownloadModalOpen;
                } else {
                  failureToast(
                    `File "${getFileName(attachmentItem.filePath)}"" not found. It may have been moved or deleted.`
                  );
                }
              }}
            >
              {getFileName(attachmentItem.filePath)}
            </button>
          {/each}
        </div>
      {/if}
    </div>
  {:else}
    <p class="text-sm text-gray-600">No attachments</p>
  {/if}
</div>

<Modal
  title={"Download " + getFileName(selectedFile)}
  id="download-attachment"
  closeOnOutsideClick={false}
  bind:showModal={isDownloadModalOpen}
  on:confirm={async () => handleFileDownload(selectedFile)}
/>
