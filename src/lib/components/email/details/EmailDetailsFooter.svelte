<script>
  import { goto } from "$app/navigation";
  import Button from "$lib/components/ui/buttons/Button.svelte";

  export let isEmailSent = false;
  export let isCommentModalOpen = false;

  function handleBackClick() {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      void goto("/email");
    }
  }
</script>

<div
  class="bottom-0 flex border-t px-2 justify-between sticky items-center overflow-none h-16"
>
  <div>
    <div class="flex h-16 flex-row items-center justify-center gap-2 py-4">
      <Button
        id="nav-profile-button"
        label="Back"
        color="white"
        on:click={handleBackClick}
      />
    </div>
  </div>
  {#if !isEmailSent}
    <div class="inline-flex">
      <div class="pr-2">
        <Button
          id="nav-add-comment-button"
          label="Add Comment"
          color="green-text-right"
          on:click={() => {
            isCommentModalOpen = true;
          }}
        />
      </div>
    </div>
  {/if}
</div>
