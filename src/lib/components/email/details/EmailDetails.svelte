<script lang="ts">
  import EmailAttachmentsAccordion from "$lib/components/email/details/EmailAttachmentsAccordion.svelte";
  import Checkbox from "$lib/components/ui/inputs/Checkbox.svelte";
  import CopyTextInput from "$lib/components/ui/inputs/CopyTextInput.svelte";

  import type { EmailById } from "$lib/types/email-table/types";

  export let emailData: EmailById;
  export let updateIsEndBalance;
  export let endBalanceIsPresent;
</script>

<div class="w-full space-y-4 p-6 flex flex-col">
  <CopyTextInput label="To" id="toField" value={emailData.to.join(", ")} />

  <CopyTextInput label="CC" id="ccField" value={emailData.cc.join(", ")} />

  {#if emailData?.attachments?.files?.length > 0}
    <EmailAttachmentsAccordion attachments={emailData.attachments.files} />
  {/if}

  <CopyTextInput label="Subject" id="subjectField" value={emailData.subject} />

  <CopyTextInput
    label="Send Date"
    id="sendDateField"
    value={emailData.sendDate}
  />

  <Checkbox
    id={emailData.id}
    label="Remove end balance from e-mail"
    checked={!endBalanceIsPresent}
    onToggle={updateIsEndBalance}
  />
</div>
