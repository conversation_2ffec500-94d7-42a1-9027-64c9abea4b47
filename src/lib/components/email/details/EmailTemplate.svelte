<script lang="ts">
  import dompurify from "dompurify";
  import { onDestroy } from "svelte";

  import LoadingSpinner from "$lib/components/ui/spinners/LoadingSpinner.svelte";

  const { sanitize } = dompurify;

  export let html: string;
  export let loadingTemplate: boolean;
  let sanitizedHtml = "";

  let htmlContainer: HTMLDivElement;

  $: sanitizedHtml = sanitize(html);

  $: if (htmlContainer && sanitizedHtml) {
    htmlContainer.innerHTML = sanitizedHtml;
  }

  onDestroy(() => {
    if (htmlContainer) {
      htmlContainer.innerHTML = "";
    }
  });
</script>

<div class="flex flex-col h-full pt-[36px] px-[36px] bg-[#f2f4f7]">
  {#if loadingTemplate}
    <div class="flex-1 flex h-full items-center justify-center">
      <LoadingSpinner extraClass="w-12 h-12" />
    </div>
  {:else}
    <div class="h-full bg-white" bind:this={htmlContainer}></div>
  {/if}
</div>
