import { fireEvent, render, screen } from "@testing-library/svelte";
import "@testing-library/jest-dom";

import { goto } from "$app/navigation";
import EmailSummaryTable from "$lib/components/email/summary/EmailSummaryTable.svelte";

import type { EmailData } from "$lib/types/email-table/types";

vi.mock("$app/navigation", () => ({ goto: vi.fn() }));

describe("EmailSummaryTable", () => {
  it("should render correctly with default props", () => {
    render(EmailSummaryTable);

    expect(screen.getByText("Client")).toBeInTheDocument();
    expect(screen.getByText("To")).toBeInTheDocument();
    expect(screen.getByText("CC")).toBeInTheDocument();
    expect(screen.getByText("Subject")).toBeInTheDocument();
    expect(screen.getByText("Amount")).toBeInTheDocument();
    expect(screen.getByText("Send Date")).toBeInTheDocument();
  });

  it("should render correctly with email data", () => {
    const emailData: EmailData[] = [
      {
        id: "5",
        clientName: "Test Client 1",
        serviceNumber: "8882390000",
        to: [],
        cc: [],
        subject: "8882390000 - Test Client 1 - Settlement Notification",
        wireAmount: "0",
        sendDate: "2025-01-02",
        endBalance: "-1234.56",
        isEndBalance: false,
        sentById: 1,
      },
      {
        id: "3",
        clientName: "Test Client 2",
        serviceNumber: "9994370000",
        to: ["<EMAIL>"],
        cc: ["<EMAIL>"],
        subject: "9994370000 - Test Client 2 - Settlement Notification",
        wireAmount: "-123.44",
        sendDate: "2025-01-03",
        endBalance: "2222.22",
        isEndBalance: true,
        sentById: 2,
      },
    ];

    render(EmailSummaryTable, { emailData });

    expect(screen.getByText("Test Client 1")).toBeInTheDocument();
    expect(screen.getByText("8882390000")).toBeInTheDocument();
    expect(screen.getByText("$0.00")).toBeInTheDocument();
    expect(screen.getByText("-$1,234.56")).toBeInTheDocument();
    expect(screen.getByText("2025-01-02")).toBeInTheDocument();

    expect(screen.getByText("Test Client 2")).toBeInTheDocument();
    expect(screen.getByText("9994370000")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(
      screen.getByText("<EMAIL>")
    ).toBeInTheDocument();
    expect(screen.getByText("-$123.44")).toBeInTheDocument();
    expect(screen.getByText("$2,222.22")).toBeInTheDocument();
    expect(screen.getByText("2025-01-03")).toBeInTheDocument();
  });

  it("should be able to sort by column", async () => {
    const emailData: EmailData[] = [
      {
        id: "5",
        clientName: "Test Client 1",
        serviceNumber: "8882390000",
        to: [],
        cc: [],
        subject: "8882390000 - Test Client 1 - Settlement Notification",
        wireAmount: "0",
        sendDate: "2025-01-02",
        endBalance: "-1234.56",
        isEndBalance: false,
        sentById: 1,
      },
    ];

    const { component } = render(EmailSummaryTable, { emailData });
    const sortChange = vi.fn();
    component.$on("sortChange", sortChange);

    const sortIcons = screen.getAllByTestId("sort-icon");

    await fireEvent.click(sortIcons[0]);
    expect(sortChange).toHaveBeenCalledTimes(1);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { sortColumnName: "client", sortDirection: "asc" },
      })
    );

    await fireEvent.click(sortIcons[0]);
    expect(sortChange).toHaveBeenCalledTimes(2);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { sortColumnName: "client", sortDirection: "desc" },
      })
    );

    await fireEvent.click(sortIcons[0]);
    expect(sortChange).toHaveBeenCalledTimes(3);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { sortColumnName: "client", sortDirection: "none" },
      })
    );

    await fireEvent.click(sortIcons[1]);
    expect(sortChange).toHaveBeenCalledTimes(4);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { sortColumnName: "subject", sortDirection: "asc" },
      })
    );

    await fireEvent.click(sortIcons[2]);
    expect(sortChange).toHaveBeenCalledTimes(5);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { sortColumnName: "wireAmount", sortDirection: "asc" },
      })
    );

    await fireEvent.click(sortIcons[3]);
    expect(sortChange).toHaveBeenCalledTimes(6);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { sortColumnName: "sendDate", sortDirection: "asc" },
      })
    );
  });

  it("should navigate to email detail page when double clicking on a row", async () => {
    const emailData: EmailData[] = [
      {
        id: "5",
        clientName: "Test Client 1",
        serviceNumber: "8882390000",
        to: [],
        cc: [],
        subject: "8882390000 - Test Client 1 - Settlement Notification",
        wireAmount: "0",
        sendDate: "2025-01-02",
        endBalance: "-1234.56",
        isEndBalance: false,
        sentById: 1,
      },
    ];

    render(EmailSummaryTable, { emailData });

    const row = screen.getByText("Test Client 1");
    await fireEvent.dblClick(row);

    expect(goto).toHaveBeenCalledTimes(1);
    expect(goto).toHaveBeenCalledWith("/email/5");
  });

  it("should display add comment overlay action when email status is pending", async () => {
    const emailData: EmailData[] = [
      {
        id: "5",
        clientName: "Test Client 1",
        serviceNumber: "8882390000",
        to: [],
        cc: [],
        subject: "8882390000 - Test Client 1 - Settlement Notification",
        wireAmount: "0",
        sendDate: "2025-01-02",
        endBalance: "-1234.56",
        isEndBalance: false,
        sentById: 1,
      },
    ];

    render(EmailSummaryTable, { emailData, emailStatus: "Email: Pending" });

    expect(screen.queryByText("Add Comment")).not.toBeInTheDocument();
    await fireEvent.click(screen.getAllByRole("checkbox")[0]);
    expect(screen.getByText("Add Comment")).toBeInTheDocument();
  });
});
