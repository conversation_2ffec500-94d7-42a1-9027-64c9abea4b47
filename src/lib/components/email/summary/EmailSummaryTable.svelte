<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import { goto } from "$app/navigation";
  import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
  import Table from "$lib/components/ui/table/Table.svelte";
  import { emailStatuses } from "$lib/constants/email";
  import { isDateBeforeToday } from "$lib/utils/date-utils";
  import { formatCurrency } from "$lib/utils/format-currency";

  import type {
    Header,
    OverlayAction,
    Row,
  } from "$lib/components/ui/table/types";
  import type { EmailData } from "$lib/types/email-table/types";

  type Direction = "none" | "asc" | "desc";

  export let emailData: EmailData[] = [];
  export let emailStatus = "";
  export let isLoading = false;
  export let isSortCleared = false;
  export let sortState: {
    id: string | number;
    direction: Direction;
  };

  const dispatch = createEventDispatcher();

  const headers: Header[] = [
    { id: "client", title: "Client", align: "left", sortable: true },
    { id: "to", title: "To", align: "left", sortable: false },
    { id: "cc", title: "CC", align: "left", sortable: false },
    { id: "subject", title: "Subject", align: "left", sortable: true },
    { id: "wireAmount", title: "Amount", align: "left", sortable: true },
    {
      id: "scheduledSendDate",
      title: "Send Date",
      align: "left",
      sortable: true,
    },
  ];

  let tableRows: Row[] = [];
  let overlayActions: OverlayAction[] = [];

  $: switch (emailStatus) {
    case emailStatuses[0]: {
      overlayActions = [
        {
          id: "addComment",
          text: "Add Comment",
          classes:
            "text-green-500 p-2 border border-gray-300 rounded-md hover:bg-green-200",
        },
      ];
      break;
    }

    default: {
      overlayActions = [];
    }
  }

  $: if (emailData.length > 0) {
    tableRows = getTableRows(emailData);
  }

  function getTableRows(emailData: EmailData[]): Row[] {
    return emailData.map((email) => ({
      id: email.id,
      showCheckbox: [emailStatuses[0]].includes(emailStatus),
      cells: [
        {
          component: ListText,
          props: {
            lines: [
              [{ text: email.clientName }],
              [{ text: email.serviceNumber, classes: "text-gray-400" }],
            ],
          },
        },
        {
          component: ListText,
          props: { lines: getAddressLines(email.to) },
        },
        {
          component: ListText,
          props: { lines: getAddressLines(email.cc) },
        },
        email.subject,
        {
          component: ListText,
          props: {
            lines: [
              [{ text: formatCurrency(email.wireAmount) }],
              [
                {
                  text: formatCurrency(email.endBalance),
                  classes: "text-gray-400",
                },
              ],
            ],
          },
        },
        {
          component: ListText,
          props: {
            lines: [
              [
                {
                  text: email.sendDate,
                  classes: `${isDateBeforeToday(email.sendDate) && "text-red-500"}`,
                },
              ],
            ],
          },
        },
      ],
    }));
  }

  function getAddressLines(emailAddresses: string[]) {
    return Array.isArray(emailAddresses)
      ? emailAddresses.map((emailAddress) => [{ text: emailAddress }])
      : [];
  }

  function handleSelectionChange(
    event: CustomEvent<{ selectedRowIds: string[] }>
  ) {
    dispatch("selectionChange", {
      selectedRowIds: event.detail.selectedRowIds,
    });
  }

  function handleOverlayAction(event: CustomEvent<{ actionId: string }>) {
    const { actionId } = event.detail;
    dispatch("overlayAction", { actionId });
  }

  async function handleRowDoubleClick(event: CustomEvent<{ rowId: string }>) {
    const { rowId } = event.detail;
    await goto(`/email/${rowId}`);
  }

  async function handleSortChange(
    event: CustomEvent<{
      id: string;
      direction: "asc" | "desc" | "none";
    }>
  ) {
    const { id, direction } = event.detail;
    dispatch("sortChange", {
      sortColumnName: id,
      sortDirection: direction,
    });
  }
</script>

<div class="w-full overflow-y-auto">
  <Table
    {headers}
    rows={tableRows}
    {overlayActions}
    showFilterIcon
    {isLoading}
    {isSortCleared}
    {sortState}
    on:sortChange={handleSortChange}
    on:rowDoubleClick={handleRowDoubleClick}
    on:selectionChange={handleSelectionChange}
    on:overlayAction={handleOverlayAction}
  />
</div>
