<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import Button from "$lib/components/ui/buttons/Button.svelte";
  import DropDownSelector from "$lib/components/ui/drop-down/DropDownSelector.svelte";

  import type { EmailStatus } from "$lib/types/email-table/types";

  const dispatch = createEventDispatcher();

  export let emailStatuses: readonly EmailStatus[] | undefined;
  export let filters: { status: EmailStatus };

  function handleStatusChange(event: CustomEvent<{ value: string }>) {
    const { value } = event.detail;
    dispatch("updateFilter", { key: "status", value });
  }
</script>

<div class="flex h-16 items-center justify-center gap-4 py-0">
  <Button
    id="filter-filters-button"
    label="Filters"
    color="none"
    on:click={() => dispatch("onToggleFilters")}
  >
    <i slot="icon" class="bi bi-filter" />
  </Button>

  <div class="h-12 mx-1 w-px bg-gray-300" />

  <Button
    id="filter-clear-all-button"
    label="Clear All"
    color="none"
    on:click={() => dispatch("clearAllFilters")}
  />

  <div class="h-12 mx-1 w-px bg-gray-300" />

  <DropDownSelector
    id="email-statuses"
    items={emailStatuses ? [...emailStatuses] : undefined}
    value={filters.status}
    labelText={filters.status}
    classOverride="flex w-60 flex-row rounded-md border shadow-sm shadow-gray-200
      bg-white border-gray-300 hover:border-gray-400 cursor-pointer"
    on:change={handleStatusChange}
  />
</div>
