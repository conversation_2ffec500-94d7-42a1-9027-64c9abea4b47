<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import DateRangePicker from "$lib/components/ui/date-pickers/DateRangePicker.svelte";
  import DropDownSelector from "$lib/components/ui/drop-down/DropDownSelector.svelte";
  import Filter from "$lib/components/ui/filter/Filter.svelte";
  import InputText from "$lib/components/ui/inputs/TextInput.svelte";
  import { debounce } from "$lib/utils/debounce";

  import type { EmailFilters } from "$lib/types/email-table/types";

  const dispatch = createEventDispatcher();

  export let entities: string[] = [];
  export let frequencies: string[] = [];
  export let merchantTypes: string[] = [];
  export let baseEmailFilters: EmailFilters;
  export let emailStatuses: string[];
  export let filters: EmailFilters = { ...baseEmailFilters };

  let hasActiveFilter = false;
  let hidden = true;

  const filterKeys = Object.keys(baseEmailFilters) as Array<keyof EmailFilters>;
  $: hasActiveFilter = filterKeys.some(
    (key) =>
      key !== "status" &&
      key !== "sortColumnName" &&
      key !== "sortDirection" &&
      filters[key] !== baseEmailFilters[key]
  );

  function debouncedHandleFilterChange() {
    debounce(() => {
      handleFilterChange();
    }, 500);
  }

  function handleFilterChange(): void {
    dispatch("filtersChange", { filters });
  }

  function clearAllFilters(): void {
    filters = { ...baseEmailFilters };
    handleFilterChange();
  }

  function handleStatusChange(event: CustomEvent<{ value: string }>): void {
    filters.status = event.detail.value;
    handleFilterChange();
  }

  function handleEntityChange(event: CustomEvent<{ value: string }>): void {
    filters.entity = event.detail.value;
    handleFilterChange();
  }

  function handleFrequencyChange(event: CustomEvent<{ value: string }>): void {
    filters.frequency = event.detail.value;
    handleFilterChange();
  }

  function handleMerchantTypeChange(
    event: CustomEvent<{ value: string }>
  ): void {
    filters.merchantType = event.detail.value;
    handleFilterChange();
  }

  function handleSendDateChange(
    event: CustomEvent<{
      startDate: Date;
      endDate: Date;
    }>
  ): void {
    const { startDate, endDate } = event.detail;

    filters.startSendDate = startDate;
    filters.endSendDate = endDate ?? startDate;

    handleFilterChange();
  }

  function handleSubjectInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    filters.subject = input.value ?? "";
    debouncedHandleFilterChange();
  }

  function handleToEmailInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    filters.toEmail = input.value ?? "";
    debouncedHandleFilterChange();
  }
</script>

<Filter on:clearFilters={clearAllFilters} {hasActiveFilter} bind:hidden>
  <!-- Filter Header -->
  <div slot="filter-header-left-append">
    <div class="flex items-center gap-2">
      <div class="h-9 w-px bg-gray-200"></div>
      <DropDownSelector
        id="email-filters-status"
        items={emailStatuses}
        value={filters.status}
        labelText={filters.status}
        on:change={handleStatusChange}
        clearable={false}
        classOverride="
          flex w-60 rounded-md border shadow-sm bg-white
          border-gray-300 hover:border-gray-400 cursor-pointer
        "
      />
    </div>
  </div>

  <!-- Filter Content -->
  <div slot="content">
    <div class="grid grid-cols-3 gap-4">
      <div class="px-2 my-2">
        <InputText
          id="email-filters-subject"
          label="Subject"
          placeholder="Enter a Subject"
          bind:value={filters.subject}
          on:input={handleSubjectInput}
        />
      </div>
      <div class="px-2 my-2">
        <InputText
          id="email-filters-to-email"
          label="To Email"
          placeholder="Enter an Email"
          bind:value={filters.toEmail}
          on:input={handleToEmailInput}
        />
      </div>
      <div class="px-2 my-2">
        <div class="mb-1.5 text-sm font-medium text-gray-800">Send Date</div>
        <DateRangePicker
          id="email-filters-send-date"
          startDate={filters?.startSendDate}
          endDate={filters?.endSendDate}
          on:change={handleSendDateChange}
        />
      </div>
      <div class="px-2 my-2">
        <DropDownSelector
          id="email-filters-entity"
          label="Entity"
          placeholder="Select an Entity"
          items={entities}
          value={filters.entity}
          labelText={filters.entity}
          on:change={handleEntityChange}
          clearable={true}
          on:clear={() => {
            filters.entity = "";
            handleFilterChange();
          }}
        />
      </div>
      <div class="px-2 my-2">
        <DropDownSelector
          id="email-filters-frequency"
          label="Frequency"
          placeholder="Select a Frequency"
          items={frequencies}
          value={filters.frequency}
          labelText={filters.frequency}
          on:change={handleFrequencyChange}
          clearable={true}
          on:clear={() => {
            filters.frequency = "";
            handleFilterChange();
          }}
        />
      </div>
      <div class="px-2 my-2">
        <DropDownSelector
          id="email-filters-merchant-type"
          label="Merchant Type"
          placeholder="Select a Merchant Type"
          items={merchantTypes}
          value={filters.merchantType}
          labelText={filters.merchantType}
          on:change={handleMerchantTypeChange}
          clearable={true}
          on:clear={() => {
            filters.merchantType = "";
            handleFilterChange();
          }}
        />
      </div>
    </div>
  </div>
</Filter>
