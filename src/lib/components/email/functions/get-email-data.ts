import { request } from "$lib/services/api-caller/request";

import type {
  EmailById,
  EmailData,
  EmailFilters,
  EmailPreference,
  EmailResponse,
} from "$lib/types/email-table/types";

type DataRequest = {
  pageNumber: number;
  recordsPerPage: number;
} & Partial<EmailFilters>;

type DataResponse = {
  totalCount: number;
  data: EmailData[];
};

export type RecordMap = Record<string, number>;

export type FilterOptionsResponse = {
  entities: RecordMap;
  frequencies: RecordMap;
  merchantTypes: RecordMap;
};

export async function getFilterOptions(): Promise<FilterOptionsResponse> {
  const response = await request.get<{ data: FilterOptionsResponse }>(
    "/email/filter-options"
  );

  return response.data;
}

export async function getEmailPreference(): Promise<EmailPreference> {
  const response = await request.get<{ data: EmailPreference }>(
    "/email/preference"
  );

  return response.data;
}

export async function fetchData({
  pageNumber,
  recordsPerPage,
  ...filters
}: DataRequest): Promise<DataResponse> {
  const offset = (pageNumber - 1) * recordsPerPage;

  const response = await request.post<{ data: DataResponse }>(`/email`, {
    offset,
    limit: recordsPerPage,
    ...filters,
  });

  return response.data;
}

export async function fetchEmailById(id: string) {
  try {
    const response = await request.get<{ data: EmailById }>(`/email/${id}`);

    return response.data;
  } catch {
    return {};
  }
}

export async function getEmailTemplate(emailId: string): Promise<string> {
  try {
    const { html = "" } = (await fetchEmailById(emailId)) as EmailResponse;

    return html;
  } catch {
    return ``;
  }
}
