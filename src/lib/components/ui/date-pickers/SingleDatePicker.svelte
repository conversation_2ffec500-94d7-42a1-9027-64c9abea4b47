<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import DatePicker from "./DatePicker.svelte";

  import type { SvelteFlatpickrProps } from "svelte-flatpickr";

  const dispatch = createEventDispatcher();

  export let id = "single-date-picker";
  export let value: Date | undefined;
  export let placeholder = "Select a date";
  let _value: Date[] = value ? [value] : [];

  const singleDateOptions: SvelteFlatpickrProps["options"] = {
    dateFormat: "Y-m-d",
    altInput: true,
    altFormat: "Y-m-d",
    onChange(selectedDates: Date[]) {
      value = selectedDates[0];
    },
  };

  $: _value = value ? [value] : [];
</script>

<DatePicker
  {id}
  options={singleDateOptions}
  bind:value={_value}
  {placeholder}
  icon="bi-calendar-range"
  on:change={(event) => dispatch("change", event)}
/>
