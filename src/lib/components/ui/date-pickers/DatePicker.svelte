<script lang="ts">
  import { createEventDispatcher } from "svelte";
  // eslint-disable-next-line max-len
  // eslint-disable-next-line import/no-named-as-default, import/no-named-as-default-member, import/default
  import Flatpickr from "svelte-flatpickr";
  // eslint-disable-next-line import/no-extraneous-dependencies
  import "flatpickr/dist/flatpickr.css";

  import type { SvelteFlatpickrProps } from "svelte-flatpickr";

  export let options: SvelteFlatpickrProps["options"] = {};
  export let placeholder = "";
  export let icon: "bi-calendar" | "bi-calendar-month" | "bi-calendar-range" =
    "bi-calendar";

  export let id;
  export let value: Date[] | undefined;
  export let formattedValue = "";
  export let hideClearButton = false;

  const dispatch = createEventDispatcher();

  function handleChange(
    event: CustomEvent<[Date[], string, SvelteFlatpickrProps["flatpickr"]]>
  ) {
    const [selectedDates, dateString, instance] = event.detail;
    dispatch("change", { selectedDates, dateStr: dateString, instance });
  }
</script>

<Flatpickr
  {options}
  bind:value
  bind:formattedValue
  on:change={handleChange}
  element="#my-picker-{id}"
>
  <div
    class="flatpickr flex w-full flex-row items-center justify-center
           self-stretch rounded-md border border-gray-300 px-5 gap-1
           dark:bg-neutral-800 dark:border-neutral-600"
    id="my-picker-{id}"
  >
    <i class={`bi ${icon} text-gray-600 dark:text-neutral-400`} />
    <input
      class="block w-full rounded-md border-0 py-3 text-gray-800
          ring-1 ring-inset ring-white focus:ring-0 dark:bg-neutral-800
          dark:ring-neutral-800 dark:placeholder:text-neutral-400 dark:text-white"
      type="text"
      {placeholder}
      data-input
      {id}
      data-testid={id}
    />

    <button
      class="input-button"
      title="clear"
      hidden={hideClearButton}
      data-clear
      on:click={(event) => {
        event.preventDefault();
        event.stopPropagation();
      }}
    >
      <i class="bi bi-x-circle text-gray-600 dark:text-neutral-400" />
    </button>
  </div>
</Flatpickr>
