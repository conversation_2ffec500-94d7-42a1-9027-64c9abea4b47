<script lang="ts">
  // eslint-disable-next-line import/no-extraneous-dependencies
  import monthSelectPlugin from "flatpickr/dist/plugins/monthSelect/index";

  import DatePicker from "./DatePicker.svelte";

  import type { SvelteFlatpickrProps } from "svelte-flatpickr";

  // eslint-disable-next-line import/no-extraneous-dependencies
  import "flatpickr/dist/plugins/monthSelect/style.css";

  export let id = "month-picker-input";
  export let value: Date | undefined;
  let _value: Date[] = value ? [value] : [];

  $: _value = value ? [value] : [];

  const monthlyOptions: SvelteFlatpickrProps["options"] = {
    dateFormat: "Y-m",
    altInput: true,
    altFormat: "Y-M",
    plugins: [
      monthSelectPlugin({
        shorthand: true,
        dateFormat: "Y-m",
      }),
    ],
    onChange(selectedDates: Date[]) {
      value = selectedDates[0] || undefined;
    },
  };
</script>

<DatePicker
  options={monthlyOptions}
  bind:value={_value}
  icon="bi-calendar-month"
  placeholder="Select a month"
  {id}
  on:change
/>
