<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import DatePicker from "./DatePicker.svelte";

  import type { SvelteFlatpickrProps } from "svelte-flatpickr";

  const dispatch = createEventDispatcher();

  export let id = "date-range-picker-input";
  export let startDate: Date | undefined;
  export let endDate: Date | undefined;
  export let placeholder = "Select a date Range";
  let _value = [startDate, endDate] as Date[];

  const dateRangeOptions: SvelteFlatpickrProps["options"] = {
    mode: "range",
    dateFormat: "Y-m-d",
    defaultDate: [startDate, endDate] as Date[],
    onChange(selectedDates: Date[]) {
      switch (selectedDates.length) {
        case 1: {
          // Handle selecting only startDate
          startDate = selectedDates[0];
          endDate = undefined;
          break;
        }

        case 2: {
          // Handle selecting both startDate and endDate
          startDate = selectedDates[0];
          endDate = selectedDates[1];
          break;
        }

        case 0: {
          // Handle clearing the dates
          startDate = undefined;
          endDate = undefined;
          break;
        }

        default: /* Skip */
      }

      dispatch("change", { startDate, endDate });
    },
    onClose(selectedDates: Date[]) {
      if (selectedDates.length <= 1) {
        dispatch("change", { startDate, endDate });
      }
    },
  };

  $: _value = [startDate, endDate] as Date[];
</script>

<DatePicker
  {id}
  options={dateRangeOptions}
  bind:value={_value}
  {placeholder}
  icon="bi-calendar-range"
  on:change={() => dispatch("change", { startDate, endDate })}
/>
