import { describe, it, expect } from "vitest";

import {
  getAvailableDays,
  frequencyConfig,
} from "$lib/components/ui/date-pickers/custom/custom-date-frequency";

describe("getAvailableDays", () => {
  it("should return correct range for Weekly-Monday", () => {
    const from = new Date("2024-06-01"); // Saturday
    const { start, end } = getAvailableDays(from, "Weekly-Monday");

    expect(start.getDay()).toBe(1); // Should be Monday

    const expectedEnd = new Date(start);
    expectedEnd.setDate(start.getDate() + 6); // Monday to Sunday

    expect(end.toDateString()).toBe(expectedEnd.toDateString());
  });

  it("should return correct range for Weekly-Friday", () => {
    const from = new Date("2024-06-01"); // Saturday
    const { start, end } = getAvailableDays(from, "Weekly-Friday");

    expect(start.getDay()).toBe(5); // Should be Friday

    const expectedEnd = new Date(start);
    expectedEnd.setDate(start.getDate() + 6); // Friday to Thursday

    expect(end.toDateString()).toBe(expectedEnd.toDateString());
  });

  it("should return correct range for Monthly", () => {
    const from = new Date("2024-06-15");
    const { start, end } = getAvailableDays(from, "Monthly");

    expect(start.getDate()).toBe(1);
    expect(end.getDate()).toBe(30); // June has 30 days
  });

  it("should return correct range for Semi-Monthly", () => {
    const from = new Date("2024-06-20");
    const { start, end } = getAvailableDays(from, "Semi-Monthly");

    expect(start.getDate()).toBe(1);
    expect(end.getDate()).toBe(15);
  });

  it("should return correct range for Twice Per Week (Tuesday)", () => {
    const from = new Date("2024-06-03"); // Monday
    const { start, end } = getAvailableDays(from, "Twice Per Week");

    expect([2, 5]).toContain(start.getDay());

    if (start.getDay() === 2) {
      expect(end.getDate()).toBe(start.getDate() + 2);
    } else {
      expect(end.getDate()).toBe(start.getDate() + 3);
    }
  });

  it("should return same date for unknown frequency", () => {
    const from = new Date("2024-06-01");
    const { start, end } = getAvailableDays(from, "Invalid");

    expect(start.toDateString()).toBe(from.toDateString());
    expect(end.toDateString()).toBe(from.toDateString());
  });
});

describe("frequencyConfig", () => {
  describe("Monthly", () => {
    it("should return the correct range for Monthly", () => {
      const from = new Date("2024-06-15");
      const { start, end } = frequencyConfig.Monthly.getRange(from);

      expect(start.getDate()).toBe(1); // First day of the month
      expect(end.getDate()).toBe(30); // Last day of the month (June has 30 days)
    });
  });

  describe("Semi-Monthly frequency", () => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const monthName = monthNames[currentMonth];
    const endOfMonth = new Date(currentYear, currentMonth + 1, 0).getDate();

    describe(`for current month (${monthName} ${currentYear})`, () => {
      it("should return correct range for 1st of month", () => {
        const firstOfMonth = new Date(currentYear, currentMonth, 1);
        const { start, end } =
          frequencyConfig["Semi-Monthly"].getRange(firstOfMonth);

        expect(start.getDate()).toBe(1);
        expect(end.getDate()).toBe(15);
        expect(start.getMonth()).toBe(currentMonth);
        expect(end.getMonth()).toBe(currentMonth);
      });

      it("should return correct range for 16th of month", () => {
        const sixteenthOfMonth = new Date(currentYear, currentMonth, 16);
        const { start, end } =
          frequencyConfig["Semi-Monthly"].getRange(sixteenthOfMonth);

        expect(start.getDate()).toBe(16);
        expect(end.getDate()).toBe(endOfMonth);
        expect(start.getMonth()).toBe(currentMonth);
        expect(end.getMonth()).toBe(currentMonth);
      });
    });
  });

  describe("Twice Per Week frequency", () => {
    const currentDate = new Date();
    const currentDay = currentDate.getDay();
    const currentDateNumber = currentDate.getDate();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Calculate dates for this week's Tuesday and Friday
    const daysUntilTuesday = (2 - currentDay + 7) % 7;
    const daysUntilFriday = (5 - currentDay + 7) % 7;

    const nextTuesday = new Date(
      currentYear,
      currentMonth,
      currentDateNumber + daysUntilTuesday
    );
    const nextFriday = new Date(
      currentYear,
      currentMonth,
      currentDateNumber + daysUntilFriday
    );

    describe(`for current week (${currentDate.toDateString()})`, () => {
      it("should return correct range when starting on Tuesday", () => {
        const { start, end } =
          frequencyConfig["Twice Per Week"].getRange(nextTuesday);

        expect(start.getDay()).toBe(2); // Tuesday
        expect(end.getDay()).toBe(4); // Thursday
        const expectedEnd = new Date(start);
        expectedEnd.setDate(start.getDate() + 2);
        expect(end.toDateString()).toBe(expectedEnd.toDateString());
      });

      it("should return correct range when starting on Friday", () => {
        const { start, end } =
          frequencyConfig["Twice Per Week"].getRange(nextFriday);

        expect(start.getDay()).toBe(5); // Friday
        expect(end.getDay()).toBe(1); // Monday

        const expectedEnd = new Date(start);
        expectedEnd.setDate(start.getDate() + 3);
        expect(end.getDate()).toBe(expectedEnd.getDate());
      });

      it("should handle month boundaries correctly", () => {
        // Force test near month end by finding last Friday of current month
        const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
        const daysInMonth = lastDayOfMonth.getDate();
        const lastFridayDate =
          daysInMonth - ((lastDayOfMonth.getDay() - 5 + 7) % 7);
        const lastFriday = new Date(currentYear, currentMonth, lastFridayDate);

        const { start, end } =
          frequencyConfig["Twice Per Week"].getRange(lastFriday);

        expect(start.getDay()).toBe(5); // Friday
        expect(end.getDay()).toBe(1); // Monday

        // Verify it wraps to next month correctly if needed
        if (lastFridayDate + 3 > daysInMonth) {
          expect(end.getMonth()).toBe((currentMonth + 1) % 12);
          expect(end.getDate()).toBe(3 - (daysInMonth - lastFridayDate));
        } else {
          expect(end.getMonth()).toBe(currentMonth);
        }
      });
    });
  });

  describe("Weekly-Friday frequency", () => {
    // Get current date and find the nearest Friday
    const currentDate = new Date();
    const currentDay = currentDate.getDay();
    const currentDateNumber = currentDate.getDate();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Calculate days until next Friday (5 is Friday)
    const daysUntilFriday = (5 - currentDay + 7) % 7;
    const nextFriday = new Date(
      currentYear,
      currentMonth,
      currentDateNumber + daysUntilFriday
    );

    describe(`for current week starting ${nextFriday.toDateString()}`, () => {
      it("should only accept Fridays as valid dates", () => {
        expect(frequencyConfig["Weekly-Friday"].validDate(nextFriday)).toBe(
          true
        );

        // Test some invalid days
        const nextThursday = new Date(nextFriday);
        nextThursday.setDate(nextFriday.getDate() - 1);
        expect(frequencyConfig["Weekly-Friday"].validDate(nextThursday)).toBe(
          false
        );

        const nextSaturday = new Date(nextFriday);
        nextSaturday.setDate(nextFriday.getDate() + 1);
        expect(frequencyConfig["Weekly-Friday"].validDate(nextSaturday)).toBe(
          false
        );
      });

      it("should return correct 7-day range from Friday to Thursday", () => {
        const { start, end } =
          frequencyConfig["Weekly-Friday"].getRange(nextFriday);

        expect(start.getDay()).toBe(5); // Friday
        expect(start.toDateString()).toBe(nextFriday.toDateString());

        expect(end.getDay()).toBe(4); // Thursday

        // Calculate the expected end date by adding 6 days to the start date
        const expectedEnd = new Date(start);
        expectedEnd.setDate(start.getDate() + 6);
        expect(end.toDateString()).toBe(expectedEnd.toDateString());
      });
      it("handles week that doesn't cross month boundary", () => {
        // Test case: Friday, May 26 2023 (last Friday of May)
        const testDate = new Date(2023, 4, 26); // May is month 4 (0-indexed)

        const { start, end } =
          frequencyConfig["Weekly-Friday"].getRange(testDate);

        // Verify start is Friday (26th)
        expect(start.getDay()).toBe(5); // Friday
        expect(start.getDate()).toBe(26);
        expect(start.getMonth()).toBe(4); // May

        // Verify end is Thursday (1st of June)
        expect(end.getDay()).toBe(4); // Thursday
        expect(end.getDate()).toBe(1);
        expect(end.getMonth()).toBe(5); // June
      });

      it("handles week that crosses month boundary", () => {
        // Test case: Friday, March 31 2023 (last Friday of March)
        const testDate = new Date(2023, 2, 31); // March is month 2

        const { start, end } =
          frequencyConfig["Weekly-Friday"].getRange(testDate);

        // Verify start is Friday (31st)
        expect(start.getDay()).toBe(5); // Friday
        expect(start.getDate()).toBe(31);
        expect(start.getMonth()).toBe(2); // March

        // Verify end is Thursday (6th of April)
        expect(end.getDay()).toBe(4); // Thursday
        expect(end.getDate()).toBe(6);
        expect(end.getMonth()).toBe(3); // April
      });
    });
  });
  describe("Weekly-Monday frequency configuration", () => {
    // Get current date and find the nearest Monday
    const currentDate = new Date();
    const currentDay = currentDate.getDay();
    const currentDateNumber = currentDate.getDate();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Calculate days until next Monday (1 is Monday)
    const daysUntilMonday = (1 - currentDay + 7) % 7;
    const nextMonday = new Date(
      currentYear,
      currentMonth,
      currentDateNumber + daysUntilMonday
    );

    describe(`for current week starting ${nextMonday.toDateString()}`, () => {
      it("should only accept Mondays as valid dates", () => {
        expect(frequencyConfig["Weekly-Monday"].validDate(nextMonday)).toBe(
          true
        );

        // Test some invalid days
        const nextSunday = new Date(nextMonday);
        nextSunday.setDate(nextMonday.getDate() - 1);
        expect(frequencyConfig["Weekly-Monday"].validDate(nextSunday)).toBe(
          false
        );

        const nextTuesday = new Date(nextMonday);
        nextTuesday.setDate(nextMonday.getDate() + 1);
        expect(frequencyConfig["Weekly-Monday"].validDate(nextTuesday)).toBe(
          false
        );
      });

      it("should return correct 7-day range from Monday to Sunday", () => {
        const { start, end } =
          frequencyConfig["Weekly-Monday"].getRange(nextMonday);

        expect(start.getDay()).toBe(1); // Monday
        expect(start.toDateString()).toBe(nextMonday.toDateString());

        expect(end.getDay()).toBe(0); // Sunday

        // Calculate the expected end date by adding 6 days to the start date
        const expectedEnd = new Date(start);
        expectedEnd.setDate(start.getDate() + 6);
        expect(end.toDateString()).toBe(expectedEnd.toDateString());
      });

      it("should handle month boundaries correctly", () => {
        // Find last Monday of current month
        const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
        const daysInMonth = lastDayOfMonth.getDate();
        const lastMondayDate =
          daysInMonth - ((lastDayOfMonth.getDay() - 1 + 7) % 7);
        const lastMonday = new Date(currentYear, currentMonth, lastMondayDate);

        const { start, end } =
          frequencyConfig["Weekly-Monday"].getRange(lastMonday);

        expect(start.getDay()).toBe(1); // Monday
        expect(end.getDay()).toBe(0); // Sunday

        // Verify it wraps to next month correctly if needed
        if (lastMondayDate + 6 > daysInMonth) {
          expect(end.getMonth()).toBe((currentMonth + 1) % 12);
          expect(end.getDate()).toBe(6 - (daysInMonth - lastMondayDate));
        } else {
          expect(end.getMonth()).toBe(currentMonth);
        }
      });
    });
  });
});
