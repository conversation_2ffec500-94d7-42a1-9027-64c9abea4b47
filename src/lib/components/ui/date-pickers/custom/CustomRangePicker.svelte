<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import DatePicker from "../DatePicker.svelte";

  import { frequencyConfig } from "$lib/components/ui/date-pickers/custom/custom-date-frequency";

  import type { SvelteFlatpickrProps } from "svelte-flatpickr";

  type Frequency =
    | "Monthly"
    | "Semi-Monthly"
    | "Twice Per Week"
    | "Weekly-Friday"
    | "Weekly-Monday";

  const dispatch = createEventDispatcher();

  export let id = "custom-range-picker-input";
  export let startDate: Date | undefined;
  export let endDate: Date | undefined;
  export let placeholder = "Select a date Range";
  export let frequency: Frequency | "" = "";

  let _value = [startDate, endDate] as Date[];

  const updateDates = (newStartDate: Date, newEndDate: Date) => {
    startDate = newStartDate;
    endDate = newEndDate;
    _value = [startDate, endDate];
    dispatch("change", { startDate, endDate });
  };

  const dateRangeOptions: SvelteFlatpickrProps["options"] = {
    mode: "single",
    dateFormat: "Y-m-d",
    defaultDate: [startDate] as Date[],

    onChange(selectedDates: Date[]) {
      if (selectedDates.length !== 1) return;

      const selectedDate = selectedDates[0];
      const config = frequencyConfig[frequency as Frequency];

      if (config?.validDate(selectedDate)) {
        const { start, end } = config.getRange(selectedDate);
        updateDates(start, end);
      }
    },

    disable: [
      (date: Date) => {
        const config = frequencyConfig[frequency as Frequency];

        return config ? !config.validDate(date) : false;
      },
    ],

    onClose(selectedDates: Date[]) {
      if (selectedDates.length === 1) {
        dispatch("change", { startDate, endDate });
      }
    },
  };

  $: _value = [startDate, endDate] as Date[];
</script>

<DatePicker
  {id}
  hideClearButton={true}
  options={dateRangeOptions}
  bind:value={_value}
  {placeholder}
  icon="bi-calendar-range"
  on:change={() => dispatch("change", { startDate, endDate })}
/>
