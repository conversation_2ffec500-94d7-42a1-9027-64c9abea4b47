export function getAvailableDays(
  from: Date,
  freq: string
): { start: Date; end: Date } {
  const date = new Date(from);
  let start: Date;
  let end: Date;

  switch (freq) {
    case "Weekly-Monday": {
      while (date.getDay() !== 1) {
        date.setDate(date.getDate() + 1);
      }

      start = new Date(date);
      end = new Date(date);
      end.setDate(start.getDate() + 6);
      break;
    }

    case "Weekly-Friday": {
      while (date.getDay() !== 5) {
        date.setDate(date.getDate() + 1);
      }

      start = new Date(date);
      end = new Date(date);
      end.setDate(start.getDate() + 6);
      break;
    }

    case "Monthly": {
      date.setDate(1);
      start = new Date(date);
      end = new Date(date);
      end.setMonth(start.getMonth() + 1);
      end.setDate(0);
      break;
    }

    case "Semi-Monthly": {
      date.setDate(1);
      start = new Date(date);
      end = new Date(date);
      end.setMonth(start.getMonth());
      end.setDate(15);
      break;
    }

    case "Twice Per Week": {
      while (date.getDay() !== 2 && date.getDay() !== 5) {
        date.setDate(date.getDate() + 1);
      }

      start = new Date(date);
      end = new Date(date);

      if (start.getDay() === 2) {
        end.setDate(start.getDate() + 2);
      } else {
        end.setDate(start.getDate() + 3);
      }

      break;
    }

    default: {
      start = new Date(date);
      end = new Date(date);
      break;
    }
  }

  return { start, end };
}

export const frequencyConfig = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  Monthly: {
    validDate: (date: Date) => date.getDate() === 1,
    getRange(date: Date) {
      const year = date.getFullYear();
      const month = date.getMonth();

      const start = new Date(year, month, 1);
      const end = new Date(year, month + 1, 0); // Last day of the month

      return { start, end };
    },
  },
  "Semi-Monthly": {
    validDate: (date: Date) => [1, 16].includes(date.getDate()),
    getRange(date: Date) {
      const year = date.getFullYear();
      const month = date.getMonth();
      const day = date.getDate();
      const endOfMonthDay = new Date(year, month + 1, 0).getDate();

      const isFirstHalf = day <= 15;

      return {
        start: new Date(year, month, isFirstHalf ? 1 : 16),
        end: new Date(year, month, isFirstHalf ? 15 : endOfMonthDay),
      };
    },
  },
  "Twice Per Week": {
    validDate: (date: Date) => [2, 5].includes(date.getDay()),

    getRange(date: Date) {
      const start = new Date(date);
      const dayOfWeek = date.getDay();
      const daysToAdd = dayOfWeek === 5 ? 3 : 2; // Friday -> Monday, Tuesday -> Thursday

      const end = new Date(date);
      end.setDate(date.getDate() + daysToAdd);

      return { start, end };
    },
  },
  "Weekly-Friday": {
    validDate: (date: Date) => date.getDay() === 5,

    getRange(date: Date) {
      const start = new Date(date);
      const end = new Date(date);
      end.setDate(date.getDate() + 6); // Add 6 days

      return { start, end };
    },
  },
  "Weekly-Monday": {
    validDate: (date: Date) => date.getDay() === 1,

    getRange(date: Date) {
      const start = new Date(date);
      const end = new Date(date);
      end.setDate(date.getDate() + 6); // Monday -> Sunday

      return { start, end };
    },
  },
};
