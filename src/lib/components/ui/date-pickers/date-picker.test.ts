import { render } from "@testing-library/svelte";

import DatePicker from "$lib/components/ui/date-pickers/DatePicker.svelte";

describe("DatePicker", () => {
  test("renders correctly with default props", () => {
    const { getByPlaceholderText } = render(DatePicker);
    expect(getByPlaceholderText("")).not.toBeNull();
  });

  test("renders correctly with provided/custom props", () => {
    const placeholder = "Select date";
    const value: Date[] = [];
    const formattedValue = "";

    const { getByPlaceholderText } = render(DatePicker, {
      placeholder,
      value,
      formattedValue,
    });
    expect(getByPlaceholderText(placeholder)).not.toBeNull();
  });
});
