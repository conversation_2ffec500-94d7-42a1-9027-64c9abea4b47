<script lang="ts">
  import { slide } from "svelte/transition";

  export let title: string;
  export let subtitle: string;
  export let otherHovered: boolean;
  export let link: string;
  let hovered = false;
</script>

<a
  rel="external"
  href={link}
  on:mouseover={() => {
    otherHovered = true;
    hovered = true;
  }}
  on:focus={() => {
    otherHovered = true;
  }}
  class="group flex flex-col {otherHovered
    ? 'bg-gray-200'
    : 'bg-blue-500'} hover:bg-blue-600 rounded p-12 max-lg:p-8 transition duration-700"
  on:mouseleave={() => {
    otherHovered = false;
    hovered = false;
  }}
  transition:slide
>
  <div class="group flex justify-between items-center">
    <h2
      class="text-[3rem] max-lg:text-[2rem] leading-tight font-semibold {otherHovered
        ? 'text-gray-400'
        : 'text-white '} group-hover:text-white transition duration-700"
    >
      {title}
    </h2>
    <div
      class="rounded-full h-24 w-24 bg-white flex items-center
      justify-center max-lg:h-16 max-lg:w-16"
    >
      <i
        class="bi bi-arrow-right text-[3rem] max-lg:text-[2rem] -rotate-45
        group-hover:rotate-0 transition {otherHovered
          ? 'text-gray-400'
          : 'text-gray-800 '} group-hover:text-gray-800"
      />
    </div>
  </div>
  {#if hovered}
    <p class=" text-xl text-white" transition:slide>
      {subtitle}
    </p>
  {/if}
</a>
