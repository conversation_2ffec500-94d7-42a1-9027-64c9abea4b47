import { cleanup, fireEvent, render, screen } from "@testing-library/svelte";
import { tick } from "svelte";

import CopyButton from "./CopyButton.svelte";

global.navigator = {
  // @ts-expect-error - interface matching
  clipboard: {
    writeText: vi.fn(),
  },
};

describe("CopyButton", () => {
  afterEach(cleanup);
  const options = {
    text: "test",
  };

  test("Should update show button and copy passed text", async () => {
    const { component } = render(CopyButton, options);

    const copyButton: HTMLElement = screen.getByTestId("copy-button-test");
    const onCopy = vi.fn();
    component.$on("copy", onCopy);

    await fireEvent.click(copyButton);
    await tick();

    expect(global.navigator.clipboard.writeText).toHaveBeenCalled();
    expect(onCopy).toHaveBeenCalled();
  });

  test("Should copy empty string when undefined", async () => {
    const { component } = render(CopyButton, options);

    const copyButton: HTMLElement = screen.getByTestId("copy-button-test");
    const onCopy = vi.fn();

    component.$on("copy", onCopy);
    component.$set({ text: undefined });
    await tick();

    await fireEvent.click(copyButton);
    await tick();

    expect(global.navigator.clipboard.writeText).toHaveBeenCalled();
    expect(onCopy).toHaveBeenCalled();
  });
});
