<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import Button from "./ButtonNew.svelte";

  const dispatch = createEventDispatcher();
  export let text: string | undefined;
  export let id = "copy-button-test";
  let textToCopy = "";

  const afterCopy = () => {
    isCopied = true;
    dispatch("copy");

    setTimeout(() => {
      isCopied = false;
    }, 1500);
  };

  const copyText = async () => {
    if ("clipboard" in navigator) {
      await navigator.clipboard.writeText(textToCopy);
    } else {
      /**
       * This is the fallback deprecated way of copying text to the clipboard.
       * Only runs if it can't find the clipboard API.
       */
      const element = document.createElement("input");

      element.type = "text";
      element.disabled = true;

      element.style.setProperty("position", "fixed");
      element.style.setProperty("z-index", "-100");
      element.style.setProperty("pointer-events", "none");
      element.style.setProperty("opacity", "0");

      element.value = textToCopy;

      document.body.append(element);

      element.click();
      element.select();
      document.execCommand("copy");

      element.remove();
    }

    afterCopy();
  };

  let isCopied = false;
  $: textToCopy = text ?? "";
</script>

<Button
  type="submit"
  intent="link"
  color="dark"
  size="small"
  {id}
  data-testid={id}
  on:click={copyText}
>
  <i class={isCopied ? "bi bi-clipboard-check" : "bi bi-clipboard"}></i>
</Button>
