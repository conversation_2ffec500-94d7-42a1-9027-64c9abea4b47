<script lang="ts">
  import { getButtonColors } from "./functions/get-button-colors";
  import LoadingSpinner from "../spinners/LoadingSpinner.svelte";

  export let label = "";
  export let isRounded = true;
  export let roundedClass = "";
  export let color = "blue";
  export let isDisabled = false;
  export let type: "button" | "submit" | "reset" | undefined = "button";
  export let isLoading = false;
  export let loaderColor = "black";
  export let height = "h-10";
  export let width = "w-full";

  if (isRounded && !roundedClass) {
    roundedClass = "rounded-md";
  }

  let textColor = "text-white fill-white";
  if (color === "white" || color === "none") {
    textColor = "text-gray-800 fill-gray-800 dark:text-white";
  } else if (color.includes("ring") || color.includes("text")) {
    textColor = "";
  }
  export let id = "";
</script>

<button
  {id}
  data-testid={id}
  {type}
  disabled={isDisabled || isLoading}
  on:click
  on:mousedown
  class="inline-flex {height} {width} min-w-max items-center justify-center px-3 py-3 text-sm
    font-semibold focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2
    {getButtonColors(color, isDisabled)}  {roundedClass}"
>
  {#if isLoading}
    <div class="p-1">
      <LoadingSpinner color={loaderColor} />
    </div>
  {/if}
  <div class="flex items-center gap-2 {textColor}">
    <slot name="icon" />
    {label}
    <slot name="follow" />
  </div>
</button>
