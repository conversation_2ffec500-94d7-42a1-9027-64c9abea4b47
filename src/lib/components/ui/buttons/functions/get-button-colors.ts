function getButtonColors(color: string, isDisabled: boolean) {
  const baseRingStyles = `bg-white shadow-sm`;
  const grayRing = `border-gray-300 border`;
  const blueText = `${baseRingStyles} text-blue-500 ${grayRing}`;
  const redText = `${baseRingStyles} text-red-500`;
  const purpleText = `${baseRingStyles} text-purple-500 ${grayRing}`;
  const colorOptions: Record<string, string> = {
    none: `text-black`,
    white: `bg-white border-gray-300 border shadow-sm dark:bg-neutral-800 
    dark:border-neutral-600`,
    red: `bg-red-500  border-none `,
    "red-text": redText,
    "red-text-left": `${redText}  ${grayRing} rounded-l-none border-l-0`,
    "red-text-no-lr": `${redText} ${grayRing} rounded-none border-x-0`,
    "red-ring": `${baseRingStyles} text-red-500 ring-red-500`,
    green: `bg-teal-500  border-none `,
    orange: `bg-orange-500  border-none `,
    blue: `bg-blue-500  border-none `,
    "blue-text": blueText,
    "blue-text-right": `${blueText} rounded-r-none`,
    "blue-text-left": `${blueText} rounded-l-none border-l`,
    "purple-text": purpleText,
    yellow: `bg-primary-500  border-none `,
    "yellow-text-left": `${baseRingStyles} text-yellow-500 ${grayRing} rounded-l-none border-l-0`,
    black: `bg-secondary  border-none `,
    "black-text": `${baseRingStyles} text-secondary ${grayRing}`,
    "green-text-right": `${baseRingStyles} text-green-500 ${grayRing} rounded-r-none`,
    "grey-text": `${baseRingStyles} text-gray-500 ${grayRing}`,
  };

  const hoverGray = `hover:bg-gray-50`;

  const hoverOptions: Record<string, string> = {
    none: hoverGray,
    white: hoverGray,
    red: `hover:bg-red-600`,
    "red-text": hoverGray,
    "red-text-left": hoverGray,
    "red-text-no-lr": hoverGray,
    "red-ring": hoverGray,
    green: `hover:bg-green-600`,
    blue: `hover:bg-blue-600`,
    "blue-text": hoverGray,
    "blue-text-right": hoverGray,
    "blue-text-left": hoverGray,
    yellow: `hover:bg-amber-500`,
    "yellow-text-left": hoverGray,
    black: `hover:bg-neutral-800 `,
    "black-text": hoverGray,
    "green-text-right": hoverGray,
    "grey-text": hoverGray,
  };

  let result = colorOptions[color];

  result += isDisabled
    ? " opacity-50 cursor-not-allowed"
    : ` ${hoverOptions[color]}`;

  return result;
}

export { getButtonColors };
