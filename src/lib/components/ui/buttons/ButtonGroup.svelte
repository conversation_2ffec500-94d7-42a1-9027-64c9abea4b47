<script lang="ts">
  import { twMerge } from "tailwind-merge";

  import { button, type ButtonConfig } from "./types/button";

  export let buttons: ButtonConfig[] = [];
</script>

<div class="inline-flex rounded-md overflow-hidden">
  {#each buttons as { label, onClick, intent, size, color, className }, index}
    <button
      on:click={onClick}
      class={twMerge(
        button({ intent, size, color }),
        className,
        index === 0 ? "rounded-l-md" : "",
        index === buttons.length - 1 ? "rounded-r-md" : "",
        index > 0 ? "-ml-px" : ""
      )}
    >
      {label}
    </button>
  {/each}
</div>
