<script lang="ts">
  import { cva, type VariantProps } from "class-variance-authority";
  import { twMerge } from "tailwind-merge";

  import type { HTMLButtonAttributes } from "svelte/elements";

  const button = cva(
    "inline-flex items-center justify-center gap-x-2 text-nowrap rounded-md border disabled:pointer-events-none disabled:opacity-50",
    {
      variants: {
        intent: {
          solid: "border-transparent",
          outline:
            "border-gray-200 bg-white shadow-sm hover:bg-gray-100 focus:bg-gray-50 dark:border-neutral-600 dark:bg-neutral-800",
          link: "border-transparent",
        },
        size: {
          small: "px-2 py-1 text-sm font-medium",
          medium: "px-4 py-3 text-sm font-medium",
        },
        color: {
          white: "text-gray-800 dark:text-white",
          blue: "text-blue-500 hover:text-blue-600",
          red: "text-red-500 hover:text-red-600",
          green: "text-green-500 hover:text-green-600",
          yellow: "text-yellow-500 hover:text-yellow-600",
          orange: "text-orange-500 hover:text-orange-600",
          purple: "text-purple-500 hover:text-purple-600",
          dark: "text-gray-800 hover:text-gray-600 dark:text-white dark:hover:text-neutral-300",
          gray: "text-gray-500 hover:text-gray-600",
        },
      },
      compoundVariants: [
        {
          intent: "solid",
          color: "white",
          class:
            "border-gray-300 bg-white hover:bg-gray-50 dark:border-neutral-600 dark:bg-neutral-800 dark:hover:bg-neutral-700",
        },
        {
          intent: "solid",
          color: "blue",
          class:
            "border-transparent bg-blue-500 text-white hover:bg-blue-600 hover:text-white focus:bg-blue-600",
        },
        {
          intent: "solid",
          color: "red",
          class:
            "border-transparent bg-red-500 text-white hover:bg-red-600 hover:text-white focus:bg-red-600",
        },
        {
          intent: "solid",
          color: "green",
          class:
            "border-transparent bg-green-500 text-white hover:bg-green-600 hover:text-white focus:bg-green-600",
        },
        {
          intent: "solid",
          color: "yellow",
          class:
            "border-transparent bg-yellow-500 text-white hover:bg-yellow-600 hover:text-white focus:bg-yellow-600",
        },
        {
          intent: "solid",
          color: "orange",
          class:
            "border-transparent bg-orange-500 text-white hover:bg-orange-600 hover:text-white focus:bg-orange-600",
        },
        {
          intent: "solid",
          color: "purple",
          class:
            "border-transparent bg-purple-500 text-white hover:bg-purple-600 hover:text-white focus:bg-purple-600",
        },
        {
          intent: "solid",
          color: "dark",
          class:
            "border-transparent bg-gray-800 text-white hover:bg-gray-700 hover:text-white focus:bg-gray-700 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-200 dark:hover:text-gray-900 dark:focus:bg-gray-200",
        },
        {
          intent: "solid",
          color: "gray",
          class:
            "border-transparent bg-gray-500 text-white hover:bg-gray-600 hover:text-white focus:bg-gray-600",
        },
      ],
      defaultVariants: {
        intent: "solid",
        size: "medium",
        color: "blue",
      },
    }
  );

  type $$Properties = Record<string, unknown> &
    Omit<HTMLButtonAttributes, "color" | "class"> &
    VariantProps<typeof button>;

  // Declare props
  export let intent: $$Properties["intent"] = "solid";
  export let size: $$Properties["size"] = "medium";
  export let color: $$Properties["color"] = "blue";
  export let className = "";

  // $$restProps contains all other props
</script>

<button
  on:click
  {...$$restProps}
  class={twMerge(button({ intent, size, color }), className)}
>
  <slot />
</button>
