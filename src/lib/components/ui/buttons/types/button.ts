import { cva, type VariantProps } from "class-variance-authority";

export const button = cva(
  "inline-flex items-center justify-center gap-x-2 text-nowrap border disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      intent: {
        solid: "border-transparent",
        outline:
          "border-gray-300 bg-white shadow-sm hover:bg-gray-100 focus:bg-gray-50 dark:border-neutral-600 dark:bg-neutral-800",
        link: "border-transparent",
      },
      size: {
        small: "px-2 py-1 text-sm font-medium",
        medium: "px-4 py-3 text-sm font-medium",
      },
      color: {
        white: "text-gray-800 dark:text-white",
        blue: "text-blue-500 hover:text-blue-600",
        red: "text-red-500 hover:text-red-600",
        green: "text-green-500 hover:text-green-600",
        yellow: "text-yellow-500 hover:text-yellow-600",
        orange: "text-orange-500 hover:text-orange-600",
        purple: "text-purple-500 hover:text-purple-600",
        dark: "text-gray-800 hover:text-gray-600 dark:text-white dark:hover:text-neutral-300",
        gray: "text-gray-500 hover:text-gray-600",
      },
    },
    compoundVariants: [
      {
        intent: "solid",
        color: "blue",
        class:
          "border-transparent bg-blue-500 text-white hover:bg-blue-600 hover:text-white focus:bg-blue-600",
      },
      {
        intent: "solid",
        color: "green",
        class:
          "border-transparent bg-green-500 text-white hover:bg-green-600 hover:text-white focus:bg-green-600",
      },
    ],
    defaultVariants: {
      intent: "solid",
      size: "medium",
      color: "blue",
    },
  }
);

export type ButtonConfig = {
  label: string;
  onClick?: () => void;
  intent?: VariantProps<typeof button>["intent"];
  size?: VariantProps<typeof button>["size"];
  color?: VariantProps<typeof button>["color"];
  className?: string;
};
