<script lang="ts">
  import { createEventDispatcher, type ComponentType } from "svelte";

  import Checkbox from "../inputs/Checkbox.svelte";

  type CardItem = {
    component: ComponentType;
    props: Record<string, unknown>;
    hasDivider?: boolean;
  };

  export let id: string;
  export let hasCheckbox = false;
  export let leadingItem: CardItem | undefined;
  export let items: Array<CardItem | undefined> = [];
  export let checked = false;

  const dispatch = createEventDispatcher();

  const handleCheckboxToggle = (id: string | number, checked: boolean) => {
    dispatch("checkboxToggle", { id, checked });
  };
</script>

<div
  {id}
  class="flex justify-between gap-4 border border-gray-200 rounded min-h-40 max-h-40 p-4 bg-white
         dark:border-neutral-700 dark:bg-neutral-800"
>
  <div class="flex gap-2 min-w-0">
    {#if hasCheckbox}
      <div class="flex items-center px-2">
        <Checkbox {id} onToggle={handleCheckboxToggle} {checked} />
      </div>
    {/if}
    {#if leadingItem}
      <svelte:component this={leadingItem.component} {...leadingItem.props} />
    {/if}
    <div class="flex gap-2 overflow-y-hidden overflow-x-scroll scrollbar -mb-3">
      {#each items as item}
        {#if item}
          {#if item.hasDivider}
            <div
              class="h-full w-px bg-gray-200 dark:bg-neutral-600 flex-shrink-0"
            ></div>
          {/if}
          <svelte:component this={item.component} {...item.props} />
        {/if}
      {/each}
    </div>
  </div>
  <slot name="actions" />
</div>
