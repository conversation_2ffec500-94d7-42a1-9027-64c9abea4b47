import type { ComponentType } from "svelte";

type Cell =
  | string
  | {
      component: ComponentType;
      props: Record<string, unknown>;
    };

type Row = {
  id: number | string;
  cells: Cell[];
  showCheckbox?: boolean;
};

type Header = {
  id: number | string;
  title: string;
  align?: "left" | "center" | "right";
  fixedWidth?: boolean;
  sortable?: boolean;
};

type OverlayButton = {
  id: number | string;
  text: string;
  classes?: string;
};

type OverlayIcon = {
  id: number | string;
  icon: string;
  tooltip?: string;
  classes?: string;
};

type OverlayAction = OverlayButton | OverlayIcon;

export {
  type Row,
  type Header,
  type Cell,
  type OverlayButton,
  type OverlayIcon,
  type OverlayAction,
};
