<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import { type OverlayAction, type OverlayButton } from "./types";

  export let selectedCount: number;
  export let overlayActions: OverlayAction[] = [];
  const dispatch = createEventDispatcher();

  function isButton(action: OverlayAction): action is OverlayButton {
    return (action as OverlayButton).text !== undefined;
  }

  function handleActionClick(action: OverlayAction) {
    dispatch("overlayAction", { actionId: action.id });
  }
</script>

<div
  class="flex gap-2 items-center dark:bg-neutral-900 text-gray-600
  dark:text-gray-500 rounded-md bg-white"
>
  <span class="py-3 px-2 font-light"> {selectedCount} selected </span>

  {#each overlayActions as action}
    {#if isButton(action)}
      <button
        class={action.classes ?? "text-gray-500 font-normal"}
        on:click={() => {
          handleActionClick(action);
        }}
      >
        {action.text}
      </button>
    {:else}
      <div class="relative group">
        <button
          class={action.classes}
          aria-label={action.tooltip ?? "icon button"}
          on:click={() => {
            handleActionClick(action);
          }}
        >
          <i class={action.icon}></i>
        </button>

        {#if action.tooltip}
          <span
            class="absolute left-4 top-4 z-10 mt-3 hidden w-36 rounded-2xl
            rounded-tl-none border bg-black/90 opacity-70 p-4 group-hover:block"
          >
            <p class="text-sm font-medium text-white">{action.tooltip}</p>
          </span>
        {/if}
      </div>
    {/if}
  {/each}
</div>
