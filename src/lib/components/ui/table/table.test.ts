import { fireEvent, render, screen } from "@testing-library/svelte";
import "@testing-library/jest-dom";

import ListText from "$lib/components/ui/table/common-cell-components/ListText.svelte";
import Table from "$lib/components/ui/table/Table.svelte";

import type {
  Header,
  OverlayAction,
  Row,
} from "$lib/components/ui/table/types";

describe("Table", () => {
  it("should render correctly with provided headers and rows", () => {
    const headers: Header[] = [
      {
        id: "test1",
        title: "Test 1",
        align: "left",
      },
      {
        id: "test2",
        title: "Test 2",
        align: "center",
        fixedWidth: false,
        sortable: true,
      },
      {
        id: "test3",
        title: "Test 3",
        align: "right",
        fixedWidth: true,
        sortable: true,
      },
    ];

    const rows: Row[] = [
      {
        id: "1",
        cells: ["Row 1 Cell 1", "Row 1 Cell 2", "Row 1 Cell 3"],
        showCheckbox: false,
      },
      {
        id: "2",
        cells: [
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Row 2" }],
                [{ text: "Cell 1", classes: "text-gray-400" }],
              ],
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Row 2" }],
                [{ text: "Cell 2", classes: "text-gray-400" }],
              ],
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Row 2" }],
                [{ text: "Cell 3", classes: "text-gray-400" }],
              ],
            },
          },
        ],
        showCheckbox: true,
      },
    ];

    render(Table, { headers, rows });

    expect(screen.getByText("Test 1")).toBeInTheDocument();
    expect(screen.getByText("Test 2")).toBeInTheDocument();
    expect(screen.getByText("Test 3")).toBeInTheDocument();

    expect(screen.getAllByTestId("sort-icon")).toHaveLength(2);

    expect(screen.getByText("Row 1 Cell 1")).toBeInTheDocument();
    expect(screen.getByText("Row 1 Cell 2")).toBeInTheDocument();
    expect(screen.getByText("Row 1 Cell 3")).toBeInTheDocument();

    expect(screen.getAllByText("Row 2")).toHaveLength(3);
    expect(screen.getByText("Cell 1")).toBeInTheDocument();
    expect(screen.getByText("Cell 2")).toBeInTheDocument();
    expect(screen.getByText("Cell 3")).toBeInTheDocument();

    expect(screen.getAllByRole("checkbox")).toHaveLength(2);
  });

  it("should be able to sort by column", async () => {
    const headers: Header[] = [
      {
        id: "test1",
        title: "Test 1",
        align: "left",
        sortable: true,
      },
      {
        id: "test2",
        title: "Test 2",
        align: "left",
        sortable: true,
      },
    ];

    const rows: Row[] = [
      {
        id: "1",
        cells: ["Row 1 Cell 1", "Row 1 Cell 2"],
        showCheckbox: true,
      },
      {
        id: "2",
        cells: [
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Row 2" }],
                [{ text: "Cell 1", classes: "text-gray-400" }],
              ],
            },
          },
          {
            component: ListText,
            props: {
              lines: [
                [{ text: "Row 2" }],
                [{ text: "Cell 2", classes: "text-gray-400" }],
              ],
            },
          },
        ],
        showCheckbox: true,
      },
    ];

    const { component, rerender } = render(Table, {
      headers,
      rows,
      showFilterIcon: true,
    });
    const sortChange = vi.fn();
    component.$on("sortChange", sortChange);

    const sortIcons = screen.getAllByTestId("sort-icon");

    expect(sortIcons[0]).toHaveClass("bi bi-filter");
    expect(sortIcons[1]).toHaveClass("bi bi-filter");

    await fireEvent.click(sortIcons[0]);
    expect(sortChange).toHaveBeenCalledTimes(1);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { id: "test1", direction: "asc" },
      })
    );
    expect(sortIcons[0]).toHaveClass("bi bi-sort-up-alt");
    expect(sortIcons[1]).toHaveClass("bi bi-filter");

    await fireEvent.click(sortIcons[0]);
    expect(sortChange).toHaveBeenCalledTimes(2);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { id: "test1", direction: "desc" },
      })
    );
    expect(sortIcons[0]).toHaveClass("bi bi-sort-down");
    expect(sortIcons[1]).toHaveClass("bi bi-filter");

    await fireEvent.click(sortIcons[1]);
    expect(sortChange).toHaveBeenCalledTimes(3);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { id: "test2", direction: "asc" },
      })
    );
    expect(sortIcons[1]).toHaveClass("bi bi-sort-up-alt");
    expect(sortIcons[0]).toHaveClass("bi bi-filter");

    await fireEvent.click(sortIcons[1]);
    expect(sortChange).toHaveBeenCalledTimes(4);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { id: "test2", direction: "desc" },
      })
    );
    expect(sortIcons[1]).toHaveClass("bi bi-sort-down");
    expect(sortIcons[0]).toHaveClass("bi bi-filter");

    await fireEvent.click(sortIcons[1]);
    expect(sortChange).toHaveBeenCalledTimes(5);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { id: "test2", direction: "none" },
      })
    );
    expect(sortIcons[1]).toHaveClass("bi bi-filter");
    expect(sortIcons[0]).toHaveClass("bi bi-filter");

    await fireEvent.click(sortIcons[1]);
    expect(sortChange).toHaveBeenCalledTimes(6);
    expect(sortChange).toHaveBeenCalledWith(
      new CustomEvent("sortChange", {
        detail: { id: "test2", direction: "asc" },
      })
    );
    expect(sortIcons[1]).toHaveClass("bi bi-sort-up-alt");
    expect(sortIcons[0]).toHaveClass("bi bi-filter");

    await rerender({ isSortCleared: true });
    expect(sortIcons[1]).toHaveClass("bi bi-filter");
    expect(sortIcons[0]).toHaveClass("bi bi-filter");
  });

  it("should be able to select rows", async () => {
    const headers: Header[] = [
      {
        id: "test1",
        title: "Test 1",
        align: "left",
      },
    ];

    const rows: Row[] = [
      {
        id: "1",
        cells: ["Row 1 Cell 1"],
        showCheckbox: true,
      },
      {
        id: "2",
        cells: ["Row 2 Cell 1"],
        showCheckbox: true,
      },
    ];

    const { component } = render(Table, { headers, rows });
    const selectionChange = vi.fn();
    component.$on("selectionChange", selectionChange);

    const checkboxes = screen.getAllByRole("checkbox");

    // Select All
    await fireEvent.click(checkboxes[0]);
    expect(selectionChange).toHaveBeenCalledTimes(1);
    expect(selectionChange).toHaveBeenCalledWith(
      new CustomEvent("selectionChange", {
        detail: { selectedRowIds: ["1", "2"] },
      })
    );

    // Unselect All
    await fireEvent.click(checkboxes[0]);
    expect(selectionChange).toHaveBeenCalledTimes(2);
    expect(selectionChange).toHaveBeenCalledWith(
      new CustomEvent("selectionChange", {
        detail: { selectedRowIds: [] },
      })
    );

    // Select Row 1
    await fireEvent.click(checkboxes[1]);
    expect(selectionChange).toHaveBeenCalledTimes(3);
    expect(selectionChange).toHaveBeenCalledWith(
      new CustomEvent("selectionChange", {
        detail: { selectedRowIds: ["1"] },
      })
    );

    // Select Row 2
    await fireEvent.click(checkboxes[2]);
    expect(selectionChange).toHaveBeenCalledTimes(4);
    expect(selectionChange).toHaveBeenCalledWith(
      new CustomEvent("selectionChange", {
        detail: { selectedRowIds: ["1", "2"] },
      })
    );

    // Unselect Row 1
    await fireEvent.click(checkboxes[1]);
    expect(selectionChange).toHaveBeenCalledTimes(5);
    expect(selectionChange).toHaveBeenCalledWith(
      new CustomEvent("selectionChange", {
        detail: { selectedRowIds: ["2"] },
      })
    );
  });

  it("should be able to click and double click rows", async () => {
    const headers: Header[] = [
      {
        id: "test1",
        title: "Test 1",
        align: "left",
      },
    ];

    const rows: Row[] = [
      {
        id: "1",
        cells: ["Row 1 Cell 1"],
        showCheckbox: true,
      },
      {
        id: "2",
        cells: ["Row 2 Cell 1"],
        showCheckbox: true,
      },
    ];

    const { component } = render(Table, { headers, rows });
    const rowSingleClick = vi.fn();
    const rowDoubleClick = vi.fn();
    component.$on("rowSingleClick", rowSingleClick);
    component.$on("rowDoubleClick", rowDoubleClick);

    const row1 = screen.getByText("Row 1 Cell 1");
    const row2 = screen.getByText("Row 2 Cell 1");

    await fireEvent.click(row1);
    expect(rowSingleClick).toHaveBeenCalledTimes(1);

    await fireEvent.dblClick(row1);
    expect(rowDoubleClick).toHaveBeenCalledTimes(1);

    await fireEvent.click(row2);
    expect(rowSingleClick).toHaveBeenCalledTimes(2);

    await fireEvent.dblClick(row2);
    expect(rowDoubleClick).toHaveBeenCalledTimes(2);
  });

  it("should hide table data when loading", () => {
    const headers: Header[] = [
      {
        id: "test1",
        title: "Test 1",
        align: "left",
      },
    ];

    const rows: Row[] = [
      {
        id: "1",
        cells: ["Row 1 Cell 1"],
        showCheckbox: true,
      },
      {
        id: "2",
        cells: ["Row 2 Cell 1"],
        showCheckbox: true,
      },
    ];

    render(Table, { headers, rows, isLoading: true });

    expect(screen.queryByText("Row 1 Cell 1")?.closest("tbody")).toHaveClass(
      "collapse"
    );
    expect(screen.queryByText("Row 2 Cell 1")?.closest("tbody")).toHaveClass(
      "collapse"
    );
  });

  it("should render provided overlay actions", async () => {
    const headers: Header[] = [
      {
        id: "test1",
        title: "Test 1",
        align: "left",
      },
    ];

    const rows: Row[] = [
      {
        id: "1",
        cells: ["Row 1 Cell 1"],
        showCheckbox: true,
      },
    ];

    const overlayActions: OverlayAction[] = [
      {
        id: "overlay-button",
        text: "Overlay Button",
      },
      {
        id: "overlay-icon",
        icon: "bi bi-x",
        tooltip: "Overlay Icon",
      },
    ];

    render(Table, { headers, rows, overlayActions });

    await fireEvent.click(screen.getAllByRole("checkbox")[0]);

    expect(screen.getByText("Overlay Button")).toBeInTheDocument();
    expect(screen.getByLabelText("Overlay Icon")).toBeInTheDocument();
  });
});
