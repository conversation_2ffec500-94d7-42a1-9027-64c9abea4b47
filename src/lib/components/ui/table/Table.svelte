<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import SelectionOverlay from "./SelectionOverlay.svelte";
  import { type Row, type Header, type OverlayAction } from "./types";

  export let headers: Header[] = [];
  export let rows: Row[] = [];
  export let overlayActions: OverlayAction[] = [];
  export let selectedRows = new Set<number | string>();
  export let showFilterIcon = false;
  export let isLoading = false;
  export let isSortCleared = false;
  export let sortState: {
    id: string | number;
    direction: Direction;
  } = {
    id: "",
    direction: "none",
  };

  $: if (isSortCleared) {
    sortState = {
      id: "",
      direction: "none",
    };
  }

  $: if (rows) {
    clearAllSelection();
  }

  $: allSelected = rows
    .filter((row) => row.showCheckbox !== false)
    .every((row) => selectedRows.has(row.id));

  $: isMultiSelect = rows.some((row) => row.showCheckbox === true);

  $: maxColumns = Math.max(
    headers.length,
    ...rows.map((row) => row?.cells?.length || 0)
  );

  type Direction = "none" | "asc" | "desc";
  const directionIcon: Record<Direction, string> = {
    none: showFilterIcon ? "bi bi-filter" : "bi bi-arrow-down-up",
    asc: "bi bi-sort-up-alt",
    desc: "bi bi-sort-down",
  };
  const dispatch = createEventDispatcher();

  const toggleSort = (id: string | number) => {
    const header = headers.find((h) => h.id === id);
    if (!header?.sortable) return;

    if (sortState.id === id) {
      const nextDirection: Direction =
        sortState.direction === "none"
          ? "asc"
          : sortState.direction === "asc"
            ? "desc"
            : "none";
      sortState = {
        id,
        direction: nextDirection,
      };
    } else {
      sortState = {
        id,
        direction: "asc",
      };
    }

    dispatch("sortChange", { id, direction: sortState.direction });
  };

  const toggleSelectAll = () => {
    const newSelectedRows = new Set<number | string>();

    if (!allSelected) {
      for (const row of rows) {
        if (row?.showCheckbox) {
          newSelectedRows.add(row.id);
        }
      }
    }

    selectedRows = newSelectedRows;
    dispatch("selectionChange", { selectedRowIds: [...selectedRows] });
  };

  const toggleSelectRow = (id: number | string) => {
    const newSelectedRows = new Set(selectedRows);
    const isSelected = newSelectedRows.has(id);

    if (isSelected) {
      newSelectedRows.delete(id);
    } else {
      if (!isMultiSelect) {
        newSelectedRows.clear();
      }

      newSelectedRows.add(id);
    }

    selectedRows = newSelectedRows;
    dispatch("selectionChange", { selectedRowIds: [...selectedRows] });
  };

  const clearAllSelection = () => {
    selectedRows = new Set<number | string>();
    dispatch("selectionChange", { selectedRowIds: [] });
  };
</script>

<div>
  <table class="w-full border-collapse dark:bg-neutral-900">
    <thead class="p-4 sticky top-0 bg-white dark:bg-neutral-900 z-10">
      {#if selectedRows.size > 0}
        <div
          class="absolute top-0 left-0 right-0 flex items-center justify-center
             {isMultiSelect ? 'ml-12' : ''}"
        >
          {#if !isMultiSelect && overlayActions.length > 0}
            <button class="p-2 bg-white" on:click={clearAllSelection}>
              <i class="bi bi-x-lg"></i>
            </button>
          {/if}
          {#if overlayActions.length > 0}
            <div class="w-full">
              <SelectionOverlay
                selectedCount={selectedRows.size}
                {overlayActions}
                on:overlayAction
              />
            </div>
          {/if}
        </div>
      {/if}
      <tr>
        {#if isMultiSelect}
          <th class="pl-2 border-b border-gray-200 dark:border-neutral-600 w-8">
            <input
              type="checkbox"
              class="selectAll w-4 h-4 border border-gray-300 rounded-sm cursor-pointer
              dark:bg-neutral-700 dark:border-neutral-600"
              checked={allSelected}
              on:click={toggleSelectAll}
            />
          </th>
        {/if}
        {#each headers as { id, title, align = "left", fixedWidth, sortable }}
          {#key fixedWidth}
            <th
              class="text-gray-600 dark:text-white font-normal text-xs
              border-b border-gray-200 dark:border-neutral-600
              py-4 px-2 {sortable
                ? 'cursor-pointer'
                : ''} text-{align} {fixedWidth && 'w-0'} whitespace-nowrap"
              on:click={() => {
                if (sortable) {
                  toggleSort(id);
                }
              }}
            >
              <span>{title}</span>
              {#if sortable}
                <i
                  class={sortState.id === id
                    ? directionIcon[sortState.direction]
                    : directionIcon.none}
                  data-testid="sort-icon"
                ></i>
              {/if}
            </th>
          {/key}
        {/each}
      </tr>
    </thead>
    <tbody class={isLoading ? "collapse" : ""}>
      {#each rows as row (row.id)}
        <tr
          class="group cursor-pointer border-b border-gray-300 dark:border-neutral-600 h-14
          {selectedRows.has(row.id)
            ? 'dark:bg-neutral-800 bg-blue-100'
            : 'hover:bg-gray-100 dark:hover:bg-neutral-800'}"
          on:click={() => {
            if (!isMultiSelect) {
              toggleSelectRow(row.id);
            }

            dispatch("rowSingleClick", { rowId: row.id });
          }}
          on:dblclick={() => {
            dispatch("rowDoubleClick", { rowId: row.id });
          }}
        >
          {#if isMultiSelect}
            <td class="pl-4 pr-2 w-8">
              {#if row?.showCheckbox}
                <input
                  type="checkbox"
                  class="selectRow w-4 h-4 border border-gray-300 dark:border-neutral-600
                   rounded-sm cursor-pointer dark:bg-neutral-700"
                  checked={selectedRows.has(row.id)}
                  on:click={(event) => {
                    event.stopPropagation();
                    toggleSelectRow(row.id);
                  }}
                  on:dblclick|stopPropagation
                />
              {/if}
            </td>
          {/if}
          {#each Array.from({ length: maxColumns })
            .fill(0)
            .map((_, index) => index) as index}
            <td class="p-2 text-{headers[index]?.align ?? 'left'}">
              {#if row.cells[index] !== undefined}
                {#if typeof row.cells[index] === "string"}
                  {row.cells[index]}
                {:else if typeof row.cells[index] === "object" && row.cells[index].component}
                  <svelte:component
                    this={row.cells[index].component}
                    {...row.cells[index].props}
                  />
                {/if}
              {/if}
            </td>
          {/each}
        </tr>
      {/each}
    </tbody>
  </table>
</div>
