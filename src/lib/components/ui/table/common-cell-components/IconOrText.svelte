<script context="module" lang="ts">
  export type IconOrText = { text: string; classes?: string; isIcon: boolean };
</script>

<script lang="ts">
  export let left: IconOrText = { text: "", isIcon: false };
  export let right: IconOrText = { text: "", isIcon: false };
  export let space = 2;
</script>

<div class={`flex items-center space-x-${space}`}>
  {#if left.isIcon}
    <i class={`${left?.classes ?? ""} ${left.text}`}></i>
  {:else}
    <p class={left?.classes ?? ""}>{left.text}</p>
  {/if}

  {#if right.isIcon}
    <i class={`${right?.classes ?? ""} ${right.text}`}></i>
  {:else}
    <p class={right?.classes ?? ""}>{right.text}</p>
  {/if}
</div>
