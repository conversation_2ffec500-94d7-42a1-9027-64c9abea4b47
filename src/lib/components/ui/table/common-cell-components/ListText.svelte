<script context="module" lang="ts">
  export type LineItem = { text: string; classes?: string };
  export type Line = [LineItem, LineItem | undefined];
</script>

<script lang="ts">
  export let lines: Line[] = [];
  export let linespace = 0;
</script>

<div class={`flex flex-col space-y-${linespace}`}>
  {#each lines as line}
    {#if line}
      <div class="flex flex-row space-x-4">
        <p class={line[0]?.classes ?? ""}>{line[0]?.text}</p>
        {#if line[1]}
          <p class={line[1]?.classes ?? ""}>{line[1]?.text}</p>
        {/if}
      </div>
    {/if}
  {/each}
</div>
