<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import { getSelectItems } from "./functions/get-select-items";
  import LoadingSpinner from "../spinners/LoadingSpinner.svelte";

  import { debounce } from "$lib/utils/debounce";

  import type { DropdownItem } from "$lib/types/ui/drop-down/types";

  const dispatch = createEventDispatcher();

  export let searchable = false;
  export let clearable = false;
  export let removable = false;

  export let label = "";
  export let labelText = "";
  export let placeholder = "Select an option";
  export let items: DropdownItem[] | string[] = [];

  export let showAll = false;
  export let formEnabled = true;

  export let isRequired = false;
  export let showRequiredInField = false;
  export let fetch:
    | undefined
    | ((_: string) => Promise<DropdownItem[]>)
    | boolean = false;

  export let value: string | undefined;
  export let id = "";
  export let hasError = false;

  export let disableItemOnSelect = false;

  export let classOverride = "";
  export let labelColor = "";

  let selectItems: DropdownItem[] = [];
  let selectedItem: DropdownItem | undefined;

  let loading = false;
  let filterText = "";

  let container: HTMLDivElement;
  let isOpen = false;

  const clearValue = () => {
    value = "";
    selectedItem = undefined;
    labelText = "";
    filterText = "";
  };

  const handleClick = async () => {
    isOpen = !isOpen;

    if (isOpen && searchable && fetch && typeof fetch === "function") {
      await fetchItems(filterText);
    }
  };

  const onRemove = () => {
    clearValue();
    dispatch("remove");
  };

  const onItemSelect = (item: DropdownItem) => {
    if (selectedItem && disableItemOnSelect) {
      selectedItem.isDisabled = false;
      item.isDisabled = true;
    }

    selectedItem = item;
    value = item.value;
    labelText = item.label;
    filterText = String(item.searchValue ?? item.label);
    isOpen = false;
    dispatch("change", item);
    dispatch("dropdownChange");
  };

  const handleClear = () => {
    clearValue();
    isOpen = false;
    dispatch("clear");
  };

  const findSelectedItem = (value: string) => {
    if (!selectItems || selectItems.length === 0) return emptyDropdownItem;

    return (
      selectItems.find((item: DropdownItem) => item.value === value) ??
      emptyDropdownItem
    );
  };

  const onInputChange = async (event: Event) => {
    const filterValue = (event.target as HTMLInputElement).value;

    debounce(async () => {
      await fetchItems(filterValue);
    });
  };

  const fetchItems = async (filter: string) => {
    if (fetch && typeof fetch === "function") {
      loading = true;
      items = await fetch(filter);
      isOpen = true;
      loading = false;
    }
  };

  const onClickOutOfContainer = (event: Event) => {
    if (isOpen && !container.contains(event.target as Node)) {
      isOpen = false;
    }
  };

  function onKeyDown(event: Event & { key: string }) {
    const actions: Record<string, (() => void) | undefined> = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Backspace() {
        if (showAll) {
          clearValue();
          isOpen = false;
        }
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      ArrowDown() {
        isOpen ||= true;
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      ArrowUp() {
        isOpen &&= false;
      },
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Escape() {
        isOpen = false;
      },
      default: undefined,
    };

    const action = actions[String(event.key)] ?? actions.default;

    if (action) {
      action();
    }
  }

  const emptyDropdownItem: DropdownItem = { value: "", label: "" };
  $: if (value) {
    selectedItem = findSelectedItem(value);

    if (selectedItem && disableItemOnSelect) {
      selectedItem.isDisabled = true;
    }

    // Select items won't update unless we add it here
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    selectItems;
    labelText = selectedItem.label;
    filterText = String(selectedItem.searchValue ?? selectedItem.label);
  }

  $: selectItems =
    items && items.length > 0 && typeof items[0] !== "object"
      ? getSelectItems(items as string[])
      : (items as DropdownItem[]);

  $: if (showAll) {
    placeholder = "All";
  }
</script>

<svelte:window on:click={onClickOutOfContainer} />
<div>
  {#if label}
    <div class="flex flex-row mb-1">
      <label
        id="{id}-label"
        data-testid="{id}-label"
        class="inline-flex flex-row items-center gap-1 mb-1 ml-0.5
        text-sm font-medium text-gray-800 dark:text-white"
        for="{id}-select"
      >
        {label}
        <slot name="tooltip" />
      </label>
      {#if isRequired}
        <div class="flex text-sm text-red-500 px-2">* Required</div>
      {/if}
    </div>
  {/if}

  <div
    {id}
    data-testid="{id}-dropdown"
    class="relative flex w-full flex-col overflow-y-visible"
    class:h-100={label}
    bind:this={container}
  >
    <div
      class=" {(classOverride && classOverride) ||
        'flex h-12 w-full flex-row gap-2 rounded-md border shadow-sm shadow-gray-200  ' +
          'dark:shadow-none bg-white border-gray-300 hover:border-gray-400' +
          ' dark:bg-neutral-800 dark:border-neutral-600 dark:hover:border-neutral-500'} "
      class:border-red-500={hasError}
      class:hover:border-red-700={hasError}
      class:shadow-red-400={hasError}
    >
      {#if formEnabled}
        <input
          type="text"
          on:pointerup|preventDefault={handleClick}
          on:keydown|stopPropagation={onKeyDown}
          on:input={onInputChange}
          bind:value={labelText}
          {placeholder}
          class="w-full rounded-lg border-0 px-2 py-3 text-sm focus:outline-none focus:ring-0
               bg-white dark:bg-neutral-800 dark:placeholder-neutral-400
                 {labelColor} dark:{labelColor || 'text-white'}"
          class:cursor-text={searchable}
          class:cursor-default={!searchable}
          readonly={!searchable}
          required={isRequired}
        />

        <div class="indicators flex flex-row self-end items-center">
          {#if loading}
            <div
              data-testid="{id}-loading-icon"
              class="py-3"
              aria-hidden="true"
            >
              <slot name="loading-icon">
                <LoadingSpinner />
              </slot>
            </div>
          {/if}

          {#if clearable && !loading && value}
            <button
              data-testid="{id}-clear-button"
              type="button"
              class="px-3 text-gray-400 dark:text-gray-600"
              on:click|preventDefault={handleClear}
            >
              <slot name="clear-icon">
                <i class="bi bi-x-lg" />
              </slot>
            </button>
          {/if}

          {#if isRequired && showRequiredInField}
            <div
              data-testid="{id}-required-icon"
              class="px-3 items-center"
              aria-hidden="true"
            >
              <slot name="required-icon">
                <div
                  class="text-red-400 flex flex-row items-center justify-center text-xs"
                >
                  Required
                </div>
              </slot>
            </div>
          {/if}

          <button type="button" on:pointerup|preventDefault={handleClick}>
            <div
              class="inline-flex items-center px-3 py-3 text-gray-500"
              aria-hidden="true"
            >
              <slot name="chevron-icon">
                <i class="bi bi-chevron-down" />
              </slot>
            </div>
          </button>
          {#if removable}
            <button
              type="button"
              class="flex h-12 items-center rounded-lg rounded-l-none
           border-l border-gray-200 px-4 text-gray-500 hover:bg-gray-200"
              on:click|preventDefault={onRemove}
              data-testid="{id}-remove-button"
            >
              <slot name="remove-icon">
                <i class="bi bi-trash3" />
              </slot>
            </button>
          {/if}
        </div>
      {:else}
        <input
          type="text"
          value={labelText}
          class="w-full rounded-lg border-0 px-2 py-3 text-sm focus:outline-none focus:ring-0"
          disabled
        />
      {/if}
    </div>
    {#if isOpen}
      <div
        class="absolute w-full overflow-hidden rounded bg-white dark:bg-neutral-800
        dark:hover:text-white mt-14 z-20"
      >
        <ul
          class="max-h-64 w-full rounded dark:border-neutral-600 overflow-y-auto scrollbar"
          class:border={selectItems.length}
        >
          {#if !loading && showAll}
            <li
              class="w-full border-b border-gray-100 px-5 py-3 text-left hover:bg-gray-50
              dark:bg-neutral-800 dark:border-neutral-600 dark:hover:bg-neutral-700"
            >
              <button
                class="w-full text-left"
                type="button"
                on:click|preventDefault={() => {
                  handleClear();
                }}
              >
                <span class="text-sm text-gray-800">All</span>
              </button>
            </li>
          {/if}
          {#each selectItems as item, index}
            {#if !item?.isDisabled || item.value === value || item?.isDefaultOrEmpty}
              <li
                class="w-full px-5 hover:bg-gray-50 dark:hover:bg-neutral-700"
                class:bg-gray-100={item.value === value}
                class:dark:bg-neutral-600={item.value === value}
              >
                <button
                  class="flex w-full flex-row justify-between py-3 text-left"
                  id="{id}-item-{index}"
                  on:click|preventDefault={() => {
                    onItemSelect(item);
                  }}
                >
                  <slot name="selection" selection={item} {index}>
                    <span
                      role="option"
                      aria-selected="true"
                      class={`self-center text-sm ${
                        item.colorClass
                          ? String(item.colorClass)
                          : "text-gray-800 dark:text-white"
                      }`}>{item.label}</span
                    >
                    {#if item.value === value}
                      <i
                        class={`${
                          item.colorClass
                            ? String(item.colorClass)
                            : "text-black dark:text-white"
                        }`}
                      >
                        <i class="bi bi-check" />
                      </i>
                    {/if}
                  </slot>
                </button>
              </li>
            {/if}
          {/each}

          {#if selectItems.length === 0 && !loading}
            <li
              class="w-full bg-gray-100 dark:bg-neutral-600 px-5 py-3 text-left hover:bg-gray-50
              dark:hover:bg-neutral-700"
            >
              <span class="text-sm text-gray-800">No items found</span>
            </li>
          {/if}
        </ul>
      </div>
    {/if}
  </div>
</div>
