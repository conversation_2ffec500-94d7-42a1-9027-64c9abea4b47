<script lang="ts">
  import { createEventDispatcher } from "svelte";

  export let label = "";
  export let id = "";
  export let options: string[] = [];
  export let value: string = options[0];
  export let isRequired = false;
  export let isDisabled = false;

  const dispatch = createEventDispatcher();

  const onChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    dispatch("dropdownChange", { value: target.value });
  };
</script>

<div {id} data-testid={id} class="w-full min-w-max flex flex-col">
  {#if label}
    <span
      id="{id}-label"
      data-testid="{id}-label"
      class="block text-sm font-medium text-gray-700 dark:text-gray-500"
    >
      {label}
    </span>
  {/if}

  <div class="w-full h-12 flex rounded-md shadow-sm">
    {#if $$slots.leadingContent}
      <span
        class="bg-white dark:bg-neutral-800 dark:text-gray-500 dark:border-neutral-600
         flex items-center rounded-l-md border
         border-r-0 border-gray-300 px-3 text-gray-500 text-sm min-w-fit"
      >
        <slot name="leadingContent" />
      </span>
    {/if}
    <select
      bind:value
      name={label}
      required={isRequired}
      id="{id}-select"
      on:change={onChange}
      class="block rounded-none border-gray-300 shadow-sm
      focus:border-wyziaYellow focus:ring-wyziaYellow sm:text-sm cursor-pointer
      dark:bg-neutral-800 dark:text-gray-500 dark:border-neutral-600"
      class:rounded-l-md={!$$slots.leadingContent}
      class:rounded-r-md={!$$slots.endingContent}
      disabled={isDisabled}
    >
      {#each options as option}
        <option id="{id}-{option}" data-testid="{id}-{option}" value={option}
          >{option}</option
        >
      {/each}
    </select>

    {#if $$slots.endingContent}
      <span
        class="bg-white dark:bg-neutral-800 dark:text-gray-500 dark:border-neutral-600
         gap-2 inline-flex items-center rounded-r-md border border-l-0
         border-gray-300 px-3 text-gray-500 text-sm"
      >
        <slot name="endingContent" />
      </span>
    {/if}
  </div>
</div>
