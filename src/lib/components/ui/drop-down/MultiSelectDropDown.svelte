<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import Button from "../buttons/ButtonNew.svelte";
  import LoadingSpinner from "../spinners/LoadingSpinner.svelte";

  import type { DropdownItem } from "$lib/types/ui/drop-down/types";

  export let items: DropdownItem[] = [];

  export let selectedItems: DropdownItem[] = [];
  export let id = "";
  export let label = "";
  export let placeholder = "Please select an item...";
  export let leftLabel = "";
  export let isRequired = false;
  export let hasError = false;
  export let clearable = true;

  export let loading = false;

  let container: HTMLDivElement;
  export let isOpen = false;

  const dispatch = createEventDispatcher();

  const onItemSelect = (item: DropdownItem) => {
    selectedItems = selectedItems.some((selectedItem) => {
      return selectedItem.value === item.value;
    })
      ? selectedItems.filter(
          (selectedItem) => selectedItem.value !== item.value
        )
      : [...selectedItems, item];
    selectedItems = selectedItems.map((selectedItem) => {
      return { label: selectedItem.label, value: selectedItem.value };
    });

    isOpen = false;
  };

  const onClickOutOfContainer = (event: Event) => {
    if (isOpen && !container.contains(event.target as Node)) {
      isOpen = false;
    }
  };

  const clearValue = () => {
    selectedItems = [];
  };

  const selectAll = () => {
    selectedItems = items.map((item) => {
      return { label: item.label, value: item.value };
    });
    isOpen = false;
  };

  const handleClick = async () => {
    if (!loading) {
      isOpen = !isOpen;
    }
  };

  const handleClear = () => {
    clearValue();
    isOpen = false;
    dispatch("clear");
  };
</script>

<svelte:window on:click={onClickOutOfContainer} />

{#if label}
  <div class="flex flex-row mb-1 justify-between">
    <label
      id="{id}-mutliselect-label"
      data-testid="{id}-mutliselect-label"
      class="inline-flex flex-row items-center gap-1 mb-1 ml-0.5 text-sm font-medium text-gray-800"
      for="{id}-select"
    >
      {label}
      <slot name="tooltip" />
    </label>
    {#if isRequired}
      <div class="flex text-sm text-red-500 px-2">Required</div>
    {/if}
  </div>
{/if}

<div
  {id}
  data-testid="{id}-multiselect-dropdown"
  class="relative flex overflow-y-visible bg-white w-full"
  bind:this={container}
>
  <div
    class="flex w-full flex-row border border-gray-300 rounded-md shadow-sm
       shadow-gray-200 bg-white"
    class:border-white={isOpen}
    class:ring-2={isOpen}
    class:ring-primary-500={isOpen}
    class:border-red-500={hasError}
    class:hover:border-red-700={hasError}
    class:shadow-red-400={hasError}
  >
    {#if leftLabel}
      <div class="text-gray-500 border-r px-2 py-3 text-sm focus:outline-none">
        {leftLabel}
      </div>
    {/if}

    <button
      id="{id}-multiselect-input"
      data-testid="{id}-multiselect-input"
      class="rounded-md border-0 px-2 py-1 text-sm focus:outline-none
         flex items-center gap-2 overflow-x-scroll w-full"
      on:click={handleClick}
      disabled={loading}
    >
      {#each selectedItems as selectedItem}
        <Button
          class="group rounded-full min-w-16"
          size="small"
          intent="outline"
          color="dark"
          on:click={() => {
            selectedItems = selectedItems.filter(
              (dropdown) => dropdown.value !== selectedItem.value
            );
          }}
        >
          <span class="truncate text-nowrap">{selectedItem.label}</span>
          <i
            class=" group-hover:bg-gray-300 bg-gray-100 h-5 w-5 rounded-full bi bi-x"
          ></i>
        </Button>
      {/each}
      {#if selectedItems.length === 0}
        <span class="text-gray-500 h-full flex items-center">{placeholder}</span
        >
      {/if}
    </button>
    <div class="flex flex-row self-end items-center pl-2">
      {#if loading}
        <div
          data-testid="{id}-loading-icon"
          class="pr-2 py-3"
          aria-hidden="true"
        >
          <slot name="loading-icon">
            <LoadingSpinner color="#6b7280" />
          </slot>
        </div>
      {/if}

      {#if clearable && !loading && selectedItems.length > 0}
        <button
          data-testid="{id}-clear-button"
          type="button"
          class="pr-2 text-gray-500"
          on:click|preventDefault={handleClear}
        >
          <slot name="clear-icon">
            <i class="bi bi-x-lg"></i>
          </slot>
        </button>
      {/if}

      <button
        type="button"
        on:pointerup|preventDefault={handleClick}
        disabled={loading}
      >
        <div
          class="inline-flex items-center pr-3 py-3 text-gray-500"
          aria-hidden="true"
        >
          <slot name="chevron-icon">
            <i class="bi bi-chevron-down"></i>
          </slot>
        </div>
      </button>
    </div>
  </div>

  {#if isOpen}
    <div class="absolute w-full overflow-hidden rounded-md bg-white mt-12 z-10">
      <ul
        class="max-h-64 w-full overflow-y-scroll rounded-md scrollbar"
        class:border={items.length}
      >
        {#if items.length > 0}
          <li class="w-full px-5 hover:bg-gray-50">
            <button
              class="flex w-full flex-row justify-between py-3 text-left"
              id="{id}-item-{-1}"
              data-testid="{id}-item-{-1}"
              on:click|preventDefault={() => {
                selectAll();
              }}
            >
              <span
                role="option"
                aria-selected="true"
                class="self-center text-sm text-gray-500">Select All</span
              >
            </button>
          </li>
        {/if}
        {#each items as item, index}
          <li
            class="w-full px-5 hover:bg-gray-50"
            class:bg-gray-100={selectedItems.find(
              (dropdown) => dropdown.value === item.value
            )}
          >
            <button
              class="flex w-full flex-row justify-between py-3 text-left"
              id="{id}-item-{index}"
              data-testid="{id}-item-{index}"
              on:click|preventDefault={() => {
                onItemSelect(item);
              }}
            >
              <slot name="selection" selection={item} {index}>
                <span
                  role="option"
                  aria-selected="true"
                  class={`self-center text-sm ${
                    item.colorClass ? String(item.colorClass) : "text-gray-800"
                  }`}>{item.label}</span
                >
                {#if selectedItems.find((dropdown) => dropdown.value === item.value)}
                  <i class="bi bi-check text-gray-500"></i>
                {/if}
              </slot>
            </button>
          </li>
        {/each}

        {#if items.length === 0}
          <li class="w-full bg-gray-100 px-5 py-3 text-left hover:bg-gray-50">
            <span class="text-sm text-gray-800">No items found</span>
          </li>
        {/if}
      </ul>
    </div>
  {/if}
</div>
