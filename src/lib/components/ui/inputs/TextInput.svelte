<script lang="ts">
  export let label = "";
  export let secondaryLabel = "";
  export let placeholder = "";
  export let feedbackMessage = "";
  export let isRequired = false;
  export let hasAutoComplete = "off";
  export let isDisabled = false;
  export let id = "";
  export let classes = "";

  export let value: string | number = "";
  export let type = "text";
</script>

<div class="flex w-full flex-col gap-2 {classes}">
  <!--Label-->
  {#if label}
    <div class="flex justify-between">
      <label
        for={id}
        class="inline-flex flex-row items-center gap-1 text-sm font-medium text-gray-800
             dark:text-white"
        data-testid={`label-${id}`}
      >
        {label}
        <!--ToolTip-->
        <slot name="tooltip" />
      </label>
      <span class="text-sm leading-6 text-gray-500">{secondaryLabel}</span>
    </div>
  {/if}

  <div class="flex h-12 w-full rounded-md shadow-sm">
    <!-- Front Slot-->
    {#if $$slots.leadingContent}
      <span
        class="inline-flex items-center gap-2 rounded-l-md border border-r-0
         border-gray-300 bg-white px-3 text-gray-500 lg:text-sm"
      >
        <slot name="leadingContent" />
      </span>
    {/if}
    <!-- InputText-->
    <input
      {id}
      disabled={isDisabled}
      data-testid={id}
      class="remove-arrow block w-full min-w-0 flex-1 rounded-none border-0
       py-1.5 text-gray-800 ring-1 ring-inset ring-gray-300
        placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:text-sm
        lg:leading-6 dark:bg-neutral-800 dark:text-white dark:ring-neutral-600
        dark:placeholder-neutral-400"
      class:rounded-l-md={!$$slots.leadingContent}
      class:rounded-r-md={!$$slots.endingContent}
      class:bg-gray-50={isDisabled}
      {placeholder}
      bind:value
      on:input
      {...{ type }}
      on:focusout={() => {
        if (typeof value === "string") {
          value = value.trim();
        }
      }}
      required={isRequired}
      autocomplete={hasAutoComplete}
    />
    <!-- Back Slot-->
    {#if $$slots.endingContent}
      <span
        class="inline-flex items-center gap-2 rounded-r-md border border-l-0 border-gray-300
         bg-white px-3 text-gray-500 lg:text-sm"
      >
        <slot name="endingContent" />
      </span>
    {/if}
  </div>
  <!--Feedback Message-->
  {#if feedbackMessage}
    <p class="text-sm text-gray-500" id="feedback-message">
      {feedbackMessage}
    </p>
  {/if}
</div>
