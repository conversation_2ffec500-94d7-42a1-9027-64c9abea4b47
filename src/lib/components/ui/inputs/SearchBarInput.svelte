<script lang="ts">
  export let value = "";
  export let placeholder = "";
</script>

<div class="flex w-full h-full border border-gray-200 rounded-md">
  <input
    class="w-full h-full border-0 focus:ring-2 rounded-md
    focus:ring-inset focus:ring-primary-500 ring-gray-300"
    type="text"
    {placeholder}
    bind:value
  />
  <button class="px-2" on:click>
    <i class="bi bi-search"></i>
  </button>
</div>
