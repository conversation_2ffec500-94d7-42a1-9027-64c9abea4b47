import { cleanup, fireEvent, render, screen } from "@testing-library/svelte";
import { tick } from "svelte";

import TextInput from "$lib/components/ui/inputs/TextInput.svelte";

describe("InputText", () => {
  afterEach(cleanup);

  // Set the initial values for the component props
  const options = {
    placeholder: "Enter text here",
    label: "Test Label",
    isRequired: false,
    id: "input-field-test",
    value: "Test value",
  };

  test("Should render label when isLabeled is true", async () => {
    render(TextInput, options);

    const label = screen.getByTestId(`label-${options.id}`);

    expect(label.textContent).toContain(options.label);
  });

  test("Should not render label when no label", async () => {
    options.label = "";
    render(TextInput, options);

    const label = screen.queryByTestId(`label-${options.id}`);

    expect(label).toBeNull();
  });

  test("Should bind value", async () => {
    render(TextInput, options);

    const input: HTMLInputElement = screen.getByTestId(options.id);

    expect(input.value).toBe(options.value);
  });

  test("Should update value when user types", async () => {
    render(TextInput, options);

    const input: HTMLInputElement = screen.getByTestId(options.id);
    await fireEvent.input(input, { target: { value: "New value" } });
    await tick();

    expect(input.value).toBe("New value");
  });
});
