<script lang="ts">
  import InputText from "./TextInput.svelte";

  export let id = "";
  export let feedbackMessage = "";
  export let placeholder = "Enter Password";
  export let label = "";
  export let password = "";
  export let isRequired = true;
  let showPassword = false;
  let inputType = "password";

  const togglePasswordVisibility = () => {
    showPassword = !showPassword;
    inputType = showPassword ? "text" : "password";
  };
</script>

<InputText
  bind:type={inputType}
  bind:value={password}
  {label}
  id="{id}-password-input"
  {placeholder}
  {isRequired}
  bind:feedbackMessage
>
  <button
    slot="endingContent"
    type="button"
    id="{id}-password-visibility-button"
    on:click={togglePasswordVisibility}
    data-testid="toggle-password-visibility-button"
  >
    {#if showPassword}
      <i class="bi bi-eye-slash" />
    {:else}
      <i class="bi bi-eye-fill" />
    {/if}
  </button>
</InputText>
