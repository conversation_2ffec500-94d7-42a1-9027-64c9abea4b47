<script lang="ts">
  export let value = "";
  export let label = "";
  export let id: string;
  export let disabled = false;
  export let placeholder = "Write your notes here...";
</script>

<div class="flex w-full flex-col gap-2">
  {#if label}
    <div class="flex justify-between">
      <label
        for={id}
        class="inline-flex flex-row items-center gap-1 text-sm font-medium text-gray-800"
        data-testid={`label-${id}`}
      >
        {label}
      </label>
    </div>
  {/if}
  <textarea
    {id}
    rows="4"
    class="block max-h-64 w-full rounded-md border border-gray-300 bg-gray-50 p-2.5 text-sm
     text-gray-900 focus:border-blue-500 focus:ring-inset focus:ring-blue-500"
    {placeholder}
    bind:value
    {disabled}
  />
</div>
