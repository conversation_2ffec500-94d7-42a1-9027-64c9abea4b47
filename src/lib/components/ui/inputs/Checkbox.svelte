<script lang="ts">
  import { createEventDispatcher } from "svelte";

  export let id: string;
  const dispatch = createEventDispatcher();

  export let label = "";
  export let checked = false;
  export let visible = true;
  export let isEditable = true;
  export let onToggle: (id: string | number, checked: boolean) => void = () => {
    // Default function does nothing
    // We need this default function of nothing because this checkbox component
    // is also being used for select all checkbox. The select all checkbox does not need
    // onToggle to pass the id but it still needs the on:change to pass the event
    // so we can stop propogation as this select all checkbox is within a button.
    // Otherwise we'll get a type error saying event does not match id, checked
    // See List object select all checkbox for clarification
  };

  const toggle = () => {
    if (onToggle) {
      onToggle(id, checked);
    }

    dispatch("toggle", { id, checked });
  };
</script>

<div class={`checkbox-container flex items-center ${visible ? "" : "hidden"}`}>
  <input
    type="checkbox"
    {id}
    bind:checked
    on:change={toggle}
    disabled={!isEditable}
    class="w-4 h-4 border border-gray-200 rounded-md cursor-pointer dark:border-neutral-600
           dark:bg-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500"
  />
  {#if label}
    <label
      for={id}
      class="inline-flex flex-row items-center ml-1 gap-1 text-sm font-medium
      text-gray-800 hover:cursor-pointer"
    >
      {label}
    </label>
  {/if}
</div>
