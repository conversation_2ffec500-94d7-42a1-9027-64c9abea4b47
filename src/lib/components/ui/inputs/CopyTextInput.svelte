<script lang="ts">
  import InputText from "./TextInput.svelte";

  import CopyButton from "$lib/components/ui/buttons/CopyButton.svelte";

  export let label: string;
  export let value: string;
  export let classes = "";
  export let id = "";
  export let isDisabled = true;
</script>

<InputText {classes} {id} {label} {value} {isDisabled}>
  <CopyButton
    slot="endingContent"
    text={value}
    color="none"
    id="{id}-copy-button"
  />
</InputText>
