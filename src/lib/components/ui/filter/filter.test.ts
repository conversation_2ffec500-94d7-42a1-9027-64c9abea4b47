import { render, fireEvent, screen } from "@testing-library/svelte";

import SlottedFilter from "./Filter.svelte";

import Filter from "$lib/components/ui/filter/Filter.svelte";

describe("Filter", () => {
  test("render filter with no slotted content", async () => {
    const { component } = render(Filter);
    const hasContent = component.$$.ctx[
      component.$$.props.viewToggle as number
    ] as boolean;

    expect(hasContent).toBe(false);
  });

  test("should toggle the filter visibility when the expand button is clicked", async () => {
    // Render the component

    render(SlottedFilter);

    // Get the expand button
    const expandButton = screen.getByTestId("filter-expand-button");

    // Initial state: hidden is true
    expect(expandButton.getAttribute("aria-expanded")).toBe("false");

    // Simulate clicking the expand button
    await fireEvent.click(expandButton);

    // After clicking: hidden is false
    expect(expandButton.getAttribute("aria-expanded")).toBe("true");

    // Simulate clicking the expand button again
    await fireEvent.click(expandButton);
    // After clicking again: hidden is true
    expect(expandButton.getAttribute("aria-expanded")).toBe("false");
  });

  test("should toggle the view when the toggle view button is clicked", async () => {
    // Render the component with isLogo set to true
    const { component } = render(SlottedFilter, { props: { isLogo: true } });

    // Get the toggle view button
    const toggleViewButton = screen.getByTestId("filter-toggle-view-button");
    let viewToggle = component.$$.ctx[
      component.$$.props.viewToggle as number
    ] as string;
    // Initial state: viewToggle is false
    expect(viewToggle).toBe(false);

    // Simulate clicking the toggle view button
    await fireEvent.click(toggleViewButton);
    viewToggle = component.$$.ctx[
      component.$$.props.viewToggle as number
    ] as string;
    // After clicking: viewToggle is true
    expect(viewToggle).toBe(true);

    // Simulate clicking the toggle view button again
    await fireEvent.click(toggleViewButton);
    viewToggle = component.$$.ctx[
      component.$$.props.viewToggle as number
    ] as string;
    // After clicking again: viewToggle is false
    expect(viewToggle).toBe(false);
  });
});
