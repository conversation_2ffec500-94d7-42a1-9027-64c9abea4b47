<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import { slide } from "svelte/transition";

  export let isLogo = false;
  export let viewToggle = false;

  const hasContent = Boolean($$slots.content);

  /**
   * Indicates whether there are active filters applied.
   * When `true` and the filter content is closed (`hidden === true`),
   * the filter icon will be displayed in red to indicate that filters are applied.
   */
  export let hasActiveFilter = false;
  export let hidden = true;
  const dispatch = createEventDispatcher();

  function clearFilters() {
    dispatch("clearFilters");
  }
</script>

<section class="dark:bg-neutral-900">
  <!--Filter Header Container-->
  <div
    class="flex h-16 w-full items-center justify-between border-b
      border-t border-gray-300 px-4 py-2 dark:border-neutral-600 dark:border-t-0"
  >
    <div class="flex items-center gap-2">
      <button
        id="filter-expand-button"
        data-testid="filter-expand-button"
        on:click={() => {
          hidden = !hidden;
        }}
        type="button"
        disabled={!hasContent}
        class="flex flex-row items-center gap-2 border-r dark:border-neutral-700 py-2 pr-4
          font-medium {hasContent
          ? 'text-gray-700 hover:text-gray-500 dark:text-white dark:hover:text-gray-200'
          : 'text-gray-300'} "
        aria-controls="disclosure-1"
        aria-expanded={!hidden}
      >
        <i
          class="bi bi-filter transform transition-transform duration-300"
          class:rotate-180={!hidden}
          class:text-red-600={hidden && hasActiveFilter}
        />
        Filters
      </button>
      <slot name="filter-search-button" />
      <button
        id="filter-clear-button"
        data-testid="filter-clear-button"
        type="button"
        disabled={!hasContent}
        class="mx-2 {hasContent
          ? 'text-gray-500 hover:text-gray-700 dark:text-neutral-500 dark:hover:text-neutral-400'
          : 'text-gray-300'}"
        on:click={clearFilters}
      >
        Clear All
      </button>
      <slot name="filter-header-left-append" />
    </div>

    <div class="flex gap-4">
      {#if isLogo}
        <button
          id="filter-toggle-view-button"
          data-testid="filter-toggle-view-button"
          on:click={() => {
            viewToggle = !viewToggle;
          }}
        >
          <i class="bi bi-grid" />
        </button>
      {/if}
      <slot name="filter-add" />
    </div>

    <slot name="filter-header-right-append" />
  </div>

  <!-- Filter Dropdown Content -->
  {#if !hidden}
    <div
      class="border-b border-gray-300 pb-4 dark:border-neutral-600"
      transition:slide={{ duration: 200 }}
    >
      <slot name="content" />
    </div>
  {/if}
</section>
