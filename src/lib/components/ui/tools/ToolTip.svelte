<script lang="ts">
  export let id: string;
  export let title: string;
  export let extraClasses: string;
</script>

<div
  class="group relative flex w-auto {extraClasses}"
  id="{id}-tooltip"
  data-testid="{id}-tooltip"
>
  <i class="bi bi-info-circle text-gray-500" />

  <span
    class=" absolute left-4 top-4 z-10 hidden w-72
       rounded-2xl rounded-tl-none border bg-black/90 p-4 group-hover:block"
    id="{id}-tooltip-content"
    data-testid="{id}-tooltip-content"
  >
    <p class="text-sm font-medium text-white">{title}</p>
    <div class="px-4 py-2 text-gray-400">
      <slot name="content" />
    </div>
  </span>
</div>
