<script lang="ts">
  import { badgeColors } from "../../../constants/theme/badge-colors";

  export let type = "";
  export let label = "";
  export let color = "";
  let isGreen = false;
  let isOrange = false;
  let isRed = false;
  let isBlue = false;
  let isWhite = false;
  let isGray = true;

  export let id = "";
  $: color ||= badgeColors.get(type.toUpperCase()) ?? "gray";

  $: if (color) {
    isGreen = color === "green";
    isOrange = color === "orange";
    isRed = color === "red";
    isBlue = color === "blue";
    isWhite = color === "white";
    isGray = color === "gray";
  }
</script>

{#if label !== ""}
  <div
    {id}
    data-testid={id}
    class="w-fit rounded-full p-1.5"
    class:bg-teal-100={isGreen}
    class:bg-orange-100={isOrange}
    class:bg-red-100={isRed}
    class:bg-gray-200={isGray}
    class:bg-blue-200={isBlue}
    class:border-gray-200={isWhite}
    class:border-2={isWhite}
  >
    <div
      class="relative flex flex-shrink-0 flex-grow-0 items-center justify-start gap-2 px-1"
    >
      <svg
        width="6"
        height="6"
        viewBox="0 0 6 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        class:fill-teal-500={isGreen}
        class:fill-red-500={isRed}
        class:fill-gray-600={isGray || isWhite}
        class:fill-blue-600={isBlue}
        class:fill-primary-500={isOrange}
        preserveAspectRatio="xMidYMid meet"
      >
        <circle cx="3" cy="3" r="3" />
      </svg>
      <p
        id="{id}-label"
        data-testid="{id}-label"
        class="text-left text-xs font-medium"
        class:text-teal-500={isGreen}
        class:text-primary-500={isOrange}
        class:text-red-500={isRed}
        class:text-gray-600={isGray}
        class:text-blue-600={isBlue}
      >
        {label}
      </p>
    </div>
  </div>
{/if}
