<script lang="ts">
  export let title = "";
  export let subtitle = "";
  export let id = "";

  export let isWhite = false; // I don't like this implementation but temp
</script>

<section {id} data-testid={id} class=" flex w-full flex-col shadow-sm">
  <div
    class="flex flex-row justify-between rounded-t-md border border-b-0 border-gray-300 p-4"
  >
    <!--Title Content-->
    <div class="flex flex-col">
      <p
        class="inline-flex flex-row items-center gap-1 text-sm font-medium text-gray-800"
      >
        {title}
        <!--ToolTip-->
        <slot name="tooltip" />
      </p>
      <p class="text-sm leading-6 text-gray-500">{subtitle}</p>
    </div>
    <!--Actions (drop, toggle)-->
    <slot name="actions" />
  </div>
  <!-- Content -->
  <div
    class="scrollbar gap-2 overflow-auto rounded-b-md border border-gray-300 bg-gray-50 p-4"
    class:bg-white={isWhite}
  >
    <slot name="content" />
  </div>
</section>
