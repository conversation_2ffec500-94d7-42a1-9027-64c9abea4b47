import { toast } from "@zerodevx/svelte-toast";

export const successToast = (message: string, options = {}, theme = {}) =>
  toast.push(message, {
    theme: {
      "--toastColor": "mintcream",
      "--toastBackground": "rgba(72,187,120,0.9)",
      "--toastBarBackground": "#2F855A",
      "--toastWidth": "35rem",
      ...theme,
    },
    initial: 0.999_999,
    next: 1,
    ...options,
  });

export const failureToast = (message: string, options = {}, theme = {}) =>
  toast.push(message, {
    theme: {
      "--toastBackground": "#EF4444",
      "--toastBarBackground": "#F87171",
      "--toastWidth": "35rem",
      ...theme,
    },
    initial: 0.999_999,
    next: 1,
    ...options,
  });

export const warningToast = (message: string, options = {}, theme = {}) =>
  toast.push(message, {
    theme: {
      "--toastBackground": "#EA580C",
      "--toastBarBackground": "#9A3412",
      "--toastWidth": "35rem",
      ...theme,
    },
    initial: 0.999_999,
    next: 1,
    ...options,
  });

export const infoToast = (message: string, options = {}, theme = {}) =>
  toast.push(message, {
    theme: {
      "--toastBackground": "#DBEAFE",
      "--toastColor": "#1E40AF",
      "--toastBarBackground": "#1E40AF",
      "--toastWidth": "35rem",
      ...theme,
    },
    initial: 0.999_999,
    next: 1,
    ...options,
  });
