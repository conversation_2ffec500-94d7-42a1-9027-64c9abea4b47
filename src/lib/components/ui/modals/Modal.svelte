<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import Button from "$lib/components/ui/buttons/Button.svelte";

  type SizeOption = "small" | "medium" | "large";

  export let showModal = false;
  export let id: string;
  export let title: string;
  export let subtitle = "";
  export let confirmLabel = "Confirm";
  export let closeOnOutsideClick = true;
  export let size: SizeOption = "small";

  let innerWidth = 0;

  const dispatch = createEventDispatcher();

  const closeModal = (event: Event) => {
    event.preventDefault();
    dispatch("cancel");
    showModal = false;
  };

  const onConfirm = () => {
    dispatch("confirm");
  };

  // Whenever we click enter request a form submit
  function requestSubmit(
    event: KeyboardEvent & { currentTarget: EventTarget & Window }
  ) {
    if (event.key === "Enter" && showModal) {
      event.preventDefault();
      const form: HTMLFormElement = document.querySelector(
        `#${id}-modal-form`
      )!;

      if (form) {
        form.requestSubmit();
      }
    }
  }
  const getSizeClasses = (size: SizeOption) => {
    switch (size) {
      case "small": {
        return "md:w-1/2";
      }

      case "medium": {
        return "md:w-3/4";
      }

      case "large": {
        return "md:w-10/12";
      }
    }
  };
</script>

<svelte:window bind:innerWidth on:keydown={requestSubmit} />

{#if showModal}
  <div
    data-testid="{id}-modal-container"
    class="t-0 fixed inset-0 z-50 flex items-center justify-center"
  >
    <div
      role="presentation"
      class="fixed inset-0 bg-black opacity-50"
      on:click={closeOnOutsideClick ? closeModal : undefined}
      data-testid="{id}-modal-background"
    />
    <div
      class="flex h-screen w-full flex-wrap content-center justify-center max-lg:p-4"
    >
      <form
        on:submit|preventDefault={onConfirm}
        class={`z-[99] flex flex-col justify-between rounded-xl bg-white
            dark:bg-stone-800 max-lg:h-full max-lg:w-full  ${getSizeClasses(size)}`}
        id="{id}-modal-form"
        data-testid="{id}-modal-form"
      >
        <!-- Modal header -->
        <div class="flex justify-between px-9 py-8 dark:text-white">
          <slot name="header">
            <div class="flex flex-col flex-wrap items-start lg:flex-nowrap">
              <h4 data-testid="{id}-modal-title">
                {title}
              </h4>
              <small>
                {#if subtitle}
                  {subtitle}
                {/if}
              </small>
            </div>
            <button
              data-testid="{id}-modal-close-button"
              on:click={closeModal}
              class="cursor-pointer text-sm text-gray-500 hover:text-secondary"
              type="button"
            >
              <i class="bi bi-x-lg" />
            </button>
          </slot>
        </div>

        <!-- Modal content -->
        <div class="overflow-y-auto px-9 my-4 lg:max-h-[30rem]">
          <slot name="content" />
        </div>

        <!-- Modal footer buttons -->
        <div class="px-9 py-8">
          <slot name="footer">
            <div class="flex gap-4 max-lg:flex-col">
              <div class="flex basis-1/2">
                <Button
                  id="{id}-default-modal-cancel-button"
                  label="Cancel"
                  color="white"
                  roundedClass="rounded-md"
                  on:click={closeModal}
                />
              </div>
              <div class="flex basis-1/2">
                <Button
                  id="{id}-default-modal-confirm-button"
                  label={confirmLabel}
                  roundedClass="rounded-md"
                  color="blue"
                  on:click={onConfirm}
                />
              </div>
            </div>
          </slot>
        </div>
      </form>
    </div>
  </div>
{/if}
