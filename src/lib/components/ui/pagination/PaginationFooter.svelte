<script lang="ts">
  import { createEventDispatcher } from "svelte";

  import DropDown from "$lib/components/ui/drop-down/DropDown.svelte";
  import Pagination from "$lib/components/ui/pagination/Pagination.svelte";
  import { recordsPerPageOptions } from "$lib/constants/theme/records-per-page";

  export let id = "footer";
  export let currentPage = 1;
  export let lastPage = 1;
  export let totalNumberOfItemsListed = 0;
  export let limit = "20";
  export let sortByValues: string[] = [];
  export let sortValue = sortByValues[0];

  const dispatch = createEventDispatcher();

  function handleDropdownChange(event: CustomEvent<{ value: string }>) {
    const { value } = event.detail;
    limit = value;
    const records = Number(value);
    dispatch("recordsPerPageChange", { records });
  }

  function handleSortFilterChange(event: CustomEvent<{ value: string }>) {
    const { value } = event.detail;
    sortValue = value;
    dispatch("sortChange", { value });
  }

  function calculateIndices(
    currentPage: number,
    limit: string,
    totalNumberOfItemsListed: number
  ) {
    const startIndex = Math.min(
      Number(limit) * (currentPage - 1) + 1,
      totalNumberOfItemsListed
    );
    const endIndex = Math.min(
      Number(limit) * currentPage,
      totalNumberOfItemsListed
    );

    return { startIndex, endIndex };
  }

  $: indices = calculateIndices(currentPage, limit, totalNumberOfItemsListed);
</script>

<div
  class="bottom-0 flex border-t px-2 justify-between sticky
         items-center overflow-none h-16 bg-white dark:bg-neutral-900
         dark:border-neutral-600"
  {id}
>
  <div>
    <Pagination
      {currentPage}
      {lastPage}
      on:pageChange={(event) => dispatch("pageChange", event.detail)}
    />
  </div>
  <div class="flex flex-row gap-4 items-center min-w-min">
    <div class="text-gray-500 whitespace-nowrap font-medium">
      <span>
        {indices.startIndex} - {indices.endIndex} / {totalNumberOfItemsListed}
      </span>
    </div>
    {#if sortByValues.length > 0}
      <DropDown
        id="{id}-sort-by"
        bind:value={sortValue}
        options={sortByValues}
        on:dropdownChange={handleSortFilterChange}
      >
        <span slot="leadingContent">Sort By</span>
      </DropDown>
    {/if}
    <DropDown
      id="{id}-records"
      bind:value={limit}
      options={recordsPerPageOptions}
      on:dropdownChange={handleDropdownChange}
    >
      <span slot="leadingContent">Records</span>
    </DropDown>
  </div>
</div>
