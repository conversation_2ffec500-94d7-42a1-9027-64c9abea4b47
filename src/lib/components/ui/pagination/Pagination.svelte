<script lang="ts">
  import { getPagesArray } from "./functions/get-pages-array";

  import ArrowButton from "$lib/components/ui/pagination/ArrowButton.svelte";
  import PageNumber from "$lib/components/ui/pagination/PageNumber.svelte";

  export let lastPage: number;
  export let currentPage: number;
  let pages: number[];

  $: pages = getPagesArray(lastPage, currentPage);
</script>

<div class="flex justify-center items-center w-full h-16 bottom-0">
  <div
    class="flex justify-start items-start flex-grow-0 flex-shrink-0
    relative overflow-hidden rounded-md bg-white border border-gray-300
    dark:bg-neutral-800 shadow-sm dark:border-neutral-600"
  >
    <ArrowButton
      on:pageChange
      bind:currentPage
      {lastPage}
      direction="previous"
    />
    <div
      class="flex-grow-0 flex-shrink-0 w-px h-10 bg-gray-200 dark:bg-neutral-600"
    />
    <div
      class="flex justify-center items-start flex-grow-0 flex-shrink-0 relative"
    >
      {#each pages as page}
        {#if page > 0}
          <PageNumber on:pageChange bind:currentPage pageNumber={page} />
        {:else}
          <div
            class="flex justify-center items-center flex-grow-0 flex-shrink-0 w-10 h-10
             relative gap-5"
          >
            <p
              class="flex-grow-0 flex-shrink-0 text-base font-medium text-center text-gray-500"
            >
              ...
            </p>
          </div>
        {/if}
        <div
          class="flex-grow-0 flex-shrink-0 w-px h-10 bg-gray-200 dark:bg-neutral-600"
        />
      {/each}
    </div>
    <ArrowButton on:pageChange bind:currentPage {lastPage} direction="next" />
  </div>
</div>
