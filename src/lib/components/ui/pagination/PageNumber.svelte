<script lang="ts">
  import { createEventDispatcher } from "svelte";

  export let currentPage: number;
  export let pageNumber: number;
  let isActive: boolean;
  const dispatch = createEventDispatcher();

  function handlePageChange() {
    dispatch("pageChange", { pageNumber });
  }

  $: isActive = currentPage === pageNumber;
</script>

<button
  on:click={handlePageChange}
  data-testid={`page-${pageNumber}`}
  class="flex justify-center items-center flex-grow-0
  flex-shrink-0 w-10 h-10 relative gap-5 bg-wyziaBlack"
  class:bg-primary-500={isActive}
  class:hover:bg-gray-100={!isActive}
  class:dark:bg-primary-500={isActive}
  class:dark:hover:bg-neutral-700={!isActive}
>
  <span
    class="flex-grow-0 flex-shrink-0 text-base font-medium text-center"
    class:text-white={isActive}
    class:text-gray-500={!isActive}
    class:dark:text-white={isActive}
    class:dark:text-gray-500={!isActive}
  >
    {pageNumber}
  </span>
</button>
