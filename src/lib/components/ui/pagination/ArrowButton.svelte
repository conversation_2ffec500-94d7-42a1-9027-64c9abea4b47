<script lang="ts">
  import { createEventDispatcher } from "svelte";

  export let currentPage: number;
  export let lastPage: number;
  export let direction: "next" | "previous" = "next";
  const dispatch = createEventDispatcher();

  const handlePageChange = () => {
    dispatch("pageChange", {
      pageNumber: direction === "next" ? currentPage + 1 : currentPage - 1,
    });
  };
</script>

<button
  disabled={direction === "next" ? currentPage === lastPage : currentPage === 1}
  on:click={handlePageChange}
  data-testid={direction === "next" ? "next-page" : "previous-page"}
  class="flex justify-start items-center flex-grow-0 flex-shrink-0 h-10
  relative gap-2 px-3 rounded-md hover:bg-gray-50 dark:hover:bg-neutral-700
  dark:text-gray-500"
>
  {#if direction === "next"}
    <!-- Bootstrap caret-right icon -->
    <span class="bi bi-chevron-right"></span>
  {:else}
    <!-- <PERSON>trap caret-left icon -->
    <span class="bi bi-chevron-left"></span>
  {/if}
</button>
