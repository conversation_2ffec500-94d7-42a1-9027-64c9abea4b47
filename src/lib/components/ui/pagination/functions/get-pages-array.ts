import { getRangeFromStartToEnd } from "./get-range-from-start-to-end";

const paginationNumbersCount = 6;

const getPagesArray = (lastPage: number, currentPage: number) => {
  // At least the first page is shown
  if (lastPage === 0) return [1];

  // If the number of pages is less than the page numbers we return the range [1..pageCount]
  if (paginationNumbersCount >= lastPage) {
    return getRangeFromStartToEnd(1, lastPage);
  }

  const leftSiblingIndex = Math.max(currentPage - 1, 1);
  const rightSiblingIndex = Math.min(currentPage + 1, lastPage);

  // Show dots if there is only one position left
  const shouldShowLeftDots = leftSiblingIndex > 2;
  const shouldShowRightDots = rightSiblingIndex < lastPage - 2;

  const firstPageIndex = 1;
  const lastPageIndex = lastPage;

  if (!shouldShowLeftDots && shouldShowRightDots) {
    const leftRange = getRangeFromStartToEnd(1, paginationNumbersCount - 1);

    return [...leftRange, -1, lastPage];
  }

  if (shouldShowLeftDots && !shouldShowRightDots) {
    const rightRange = getRangeFromStartToEnd(
      lastPage - paginationNumbersCount + 2,
      lastPage
    );

    return [firstPageIndex, -1, ...rightRange];
  }

  if (shouldShowLeftDots && shouldShowRightDots) {
    const middleRange = getRangeFromStartToEnd(
      leftSiblingIndex,
      rightSiblingIndex
    );

    return [firstPageIndex, -1, ...middleRange, -1, lastPageIndex];
  }

  return [];
};

export { getPagesArray };
