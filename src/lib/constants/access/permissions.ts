// We chose to define `permissions` as an object where the key and value are the same
// for easier access throughout the application. This ensures that permissions can be
// referenced consistently using dot notation (e.g., `permissions.read`), providing
// more clarity and avoiding any mismatch between keys and values.

const permissions = {
  read: "read",
  update: "update",
  create: "create",
  delete: "delete",
  approve: "approve",
  reset: "reset",
  send: "send",
} as const;

type Permission = keyof typeof permissions;

export { type Permission, permissions };
