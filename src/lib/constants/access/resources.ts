// We chose to define `resources` as an object where the key and value are the same
// for easier access throughout the application. This allows for more convenient
// and explicit referencing using dot notation (e.g., `resources.users`),
// instead of relying on array indexing or find/filter operations.

const resources = {
  users: "users",
  filesystem: "filesystem",
  iam: "iam",
  settlement: "settlement",
  allMonthlySettlement: "allMonthlySettlement",
  trueUp: "trueUp",
  documentation: "documentation",
  tierset: "tierset",
  wire: "wire",
  email: "email",
  merchantConfiguration: "merchantConfiguration",
  reserveFund: "reserveFund",
  seedDataMaintenance: "seedDataMaintenance",
} as const;

type Resource = keyof typeof resources;

export { type Resource, resources };
