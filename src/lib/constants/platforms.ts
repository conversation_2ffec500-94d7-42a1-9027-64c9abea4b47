import type { Platform } from "$lib/types/settlement/types";

const payInGroup: Platform[] = ["ETI", "RFM", "IDP", "ETF"];
const payOutGroup: Platform[] = ["ETO", "RTO", "RTX", "ACH", "ANR", "ANX"];
const kycGroup: Platform[] = ["KYC"];
const minimumFeeGroup: Platform[] = ["ETI", "RFM"];
const partialReturnGroup: Platform[] = ["RTO"];
const rejected1Group: Platform[] = ["RTO"];

export {
  payInGroup,
  payOutGroup,
  kycGroup,
  minimumFeeGroup,
  partialReturnGroup,
  rejected1Group,
};
