import { permissions } from "$lib/constants/access/permissions";
import { resources } from "$lib/constants/access/resources";

const tabs = [
  {
    name: "Admin",
    links: [
      {
        name: "Users",
        icon: "bi bi-people",
        resource: resources.users,
        permission: permissions.read,
      },
      {
        name: "I<PERSON>",
        icon: "bi bi-shield-lock",
        resource: resources.iam,
        permission: permissions.read,
      },
    ],
  },
  {
    name: "Finance",
    links: [
      {
        name: "Email",
        icon: "bi bi-envelope",
        resource: resources.email,
        permission: permissions.read,
      },
      {
        name: "Settlement",
        icon: "bi bi-journal-check",
        resource: resources.allMonthlySettlement,
        permission: permissions.read,
      },
      {
        name: "File System",
        icon: "bi bi-cloud-arrow-down",
        resource: resources.filesystem,
        permission: permissions.read,
      },
    ],
  },
  {
    name: "BI",
    links: [
      {
        name: "True Up",
        icon: "bi bi-graph-up",
        resource: resources.trueUp,
        permission: permissions.read,
      },
    ],
  },
];

export { tabs };
