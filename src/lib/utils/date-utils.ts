import { parseISO, startOfDay, isBefore, format } from "date-fns";

function isDateBeforeToday(date: string): boolean {
  const today = startOfDay(new Date());
  const parsedDate = startOfDay(parseISO(date));

  return isBefore(parsedDate, today);
}

function formatDate(date: Date): string {
  return format(date, "MM/dd/yyyy hh:mm:ss a");
}

// eslint-disable-next-line @typescript-eslint/naming-convention
function formatDateStringToYYYYMMDD(date: string | Date): string {
  if (typeof date === "string") {
    // If the date only has month day and year (e.g., "2023-01-01"),
    // we need to ensure it created a date properly because new Date will
    // go back a few hours in MST browsers

    return format(new Date(`${date}T00:00:00`), "yyyy-MM-dd");
  }

  return format(new Date(date), "yyyy-MM-dd");
}

// eslint-disable-next-line @typescript-eslint/naming-convention
function formatDateStringToDDMMYYYY(date: string | Date): string {
  if (typeof date === "string") {
    // If the date only has month day and year (e.g., "2023-01-01"),
    // we need to ensure it created a date properly because new Date will
    // go back a few hours in MST browsers

    return format(new Date(date + "T00:00:00"), "dd-MM-yyyy");
  }

  return format(new Date(date), "dd-MM-yyyy");
}

export {
  formatDate,
  isDateBeforeToday,
  formatDateStringToYYYYMMDD,
  formatDateStringToDDMMYYYY,
};
