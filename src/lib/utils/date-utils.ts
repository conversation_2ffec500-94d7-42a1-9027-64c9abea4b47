import { parseISO, startOfDay, isBefore, format } from "date-fns";

function isDateBeforeToday(date: string): boolean {
  const today = startOfDay(new Date());
  const parsedDate = startOfDay(parseISO(date));

  return isBefore(parsedDate, today);
}

function formatDate(date: Date): string {
  return format(date, "MM/dd/yyyy hh:mm:ss a");
}

// eslint-disable-next-line @typescript-eslint/naming-convention
function formatDateStringToYYYYMMDD(date: string | Date): string {
  return format(new Date(date), "yyyy-MM-dd");
}

export { formatDate, isDateBeforeToday, formatDateStringToYYYYMMDD };
