import { get } from "svelte/store";

import { type Permission } from "$lib/constants/access/permissions";
import { type Resource } from "$lib/constants/access/resources";
import { authStore } from "$lib/stores/auth-store";

/**
 * Checks if the user has the specified permission for a given resource using union logic.
 * This means that if any of the user's roles has the required permission for the resource,
 * the user is considered to have that permission.
 *
 * Example:
 * If `permissionTocheck` is `'read'` and the user has the `read` permission in one of their
 * roles for the resource, the function will return `true`.
 *
 * @param {Resource} _resource - The resource for which the permission is being checked.
 * @param {Permission} _permissionTocheck - The permission to check against the user's roles.
 * @returns {boolean} - Returns true if the user has the required permission in any role,
 * otherwise false.
 */
function checkPermission(
  resource: Resource,
  permissionTocheck: Permission
): boolean {
  // Returning true for now as role assignment through the UI is not yet implemented.
  // To prevent developers from being blocked by this permission check, we are temporarily
  // bypassing it by returning true until the UI is ready to support role assignment for users.

  // Remove the return true below for the actual implementation once the UI is ready.

  return true;
  const { userProfile } = get(authStore);

  const rules = userProfile?.roles?.map((role) => role.rule) ?? [];

  return _checkPermission(resource, permissionTocheck, rules);
}

function _checkPermission(
  resource: Resource,
  permission: Permission,
  rules: Array<Record<Resource, Permission[]>>
): boolean {
  return rules.some((rule) => {
    const resourcePermissions = rule[resource] || [];

    return resourcePermissions.includes(permission);
  });
}

// Note: _checkPermission is exported for testing purposes only.
export { checkPermission, _checkPermission };
