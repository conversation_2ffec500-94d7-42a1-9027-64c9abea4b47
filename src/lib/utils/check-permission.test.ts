import { describe, it, expect } from "vitest";

import { _checkPermission } from "./check-permission";

import { type Permission } from "$lib/constants/access/permissions";
import { type Resource } from "$lib/constants/access/resources";

describe("_checkPermission", () => {
  const sampleRules: Array<Record<Resource, Permission[]>> = [
    {
      users: ["read", "update"],
      filesystem: ["read", "delete"],
      iam: ["read", "approve"],
      settlement: ["create", "update"],
      allMonthlySettlement: [],
      documentation: ["read"],
      tierset: ["create"],
      wire: ["approve"],
      email: ["send"],
      trueUp: ["read"],
      merchantConfiguration: ["read"],
      reserveFund: [],
      seedDataMaintenance: [],
    },
    {
      users: ["read"],
      filesystem: ["create", "delete"],
      iam: ["update"],
      settlement: ["create"],
      allMonthlySettlement: ["read"],
      documentation: ["update"],
      tierset: ["update"],
      wire: ["reset"],
      email: ["approve", "send"],
      trueUp: ["update"],
      merchantConfiguration: [],
      reserveFund: ["update"],
      seedDataMaintenance: ["create"],
    },
  ];

  it("should return true when the permission exists for the resource", () => {
    const result = _checkPermission("users", "read", sampleRules);
    expect(result).toBe(true);
  });

  it("should return false when the permission does not exist for the resource", () => {
    const result = _checkPermission("users", "delete", sampleRules);
    expect(result).toBe(false);
  });

  it("should return true when the permission exists in any rule for the resource", () => {
    const result = _checkPermission("iam", "approve", sampleRules);
    expect(result).toBe(true);
  });

  it("should return false if no rules match the resource and permission", () => {
    const result = _checkPermission("email", "read", sampleRules);
    expect(result).toBe(false);
  });

  it("should return false when there are no rules defined for the resource", () => {
    const emptyRules: Array<Record<Resource, Permission[]>> = [];
    const result = _checkPermission("users", "read", emptyRules);
    expect(result).toBe(false);
  });
});
