import { formatCurrency } from "./format-currency";

describe("formatCurrency", () => {
  it("should format a valid numeric string correctly", () => {
    expect(formatCurrency("1234.56")).toBe("$1,234.56");
  });

  it("should format a valid integer string correctly", () => {
    expect(formatCurrency("1000")).toBe("$1,000.00");
  });

  it("should return $0.00 for an invalid numeric string", () => {
    expect(formatCurrency("abc")).toBe("$0.00");
  });

  it("should return $0.00 for an empty string", () => {
    expect(formatCurrency("")).toBe("$0.00");
  });

  it("should format a negative numeric string correctly", () => {
    expect(formatCurrency("-1234.56")).toBe("-$1,234.56");
  });

  it("should format a string with leading/trailing spaces correctly", () => {
    expect(formatCurrency(" 1234.56 ")).toBe("$1,234.56");
  });

  it("should format a number correctly", () => {
    expect(formatCurrency(1234.56)).toBe("$1,234.56");
  });

  it("should format a negative number correctly", () => {
    expect(formatCurrency(-1234.56)).toBe("-$1,234.56");
  });

  it("should format a number with a long decimal part", () => {
    expect(formatCurrency(1000.519_238_029)).toBe("$1,000.52");
  });

  it("should format a number with no decimal part correctly", () => {
    expect(formatCurrency(1000)).toBe("$1,000.00");
  });

  it("should handle large numbers correctly", () => {
    expect(formatCurrency(1_234_567_890.12)).toBe("$1,234,567,890.12");
  });

  it("should handle large negative numbers correctly", () => {
    expect(formatCurrency(-1_234_567_890.12)).toBe("-$1,234,567,890.12");
  });

  it("should handle zero correctly", () => {
    expect(formatCurrency(0)).toBe("$0.00");
  });

  it("should format a number with leading zeros correctly", () => {
    expect(formatCurrency("0001234.56")).toBe("$1,234.56");
  });

  it("should format a number with trailing zeros correctly", () => {
    expect(formatCurrency("1234.5600")).toBe("$1,234.56");
  });
});
