import { formatCurrency } from "./format-currency";

describe("formatCurrency", () => {
  it("should format a valid numeric string correctly", () => {
    expect(formatCurrency("1234.56")).toBe("$1,234.56");
  });

  it("should format a valid integer string correctly", () => {
    expect(formatCurrency("1000")).toBe("$1,000.00");
  });

  it("should return $0.00 for an invalid numeric string", () => {
    expect(formatCurrency("abc")).toBe("$0.00");
  });

  it("should return $0.00 for an empty string", () => {
    expect(formatCurrency("")).toBe("$0.00");
  });

  it("should format a negative numeric string correctly", () => {
    expect(formatCurrency("-1234.56")).toBe("-$1,234.56");
  });

  it("should format a string with leading/trailing spaces correctly", () => {
    expect(formatCurrency(" 1234.56 ")).toBe("$1,234.56");
  });
});
