name: CD-fa-frontend

on:
  push:
    branches:
      - development
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

concurrency:
  group: CD-fa-frontend

jobs:
  S3-Deploy-dev:
    runs-on: ubuntu-latest
    environment: Development

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: <PERSON><PERSON><PERSON> sha
        run: |
          SHA_SHORT=$(echo $GITHUB_SHA | cut -c1-7) 
          echo "SHA_SHORT=$SHA_SHORT" >> $GITHUB_ENV

      - name: Set up Node.js Version 20
        uses: actions/setup-node@v3
        with:
          node-version: "20.18.0"
      - name: Install Dependencies & Build
        run: |
          npm install
          npm run build
        env:
          PUBLIC_API_URL: ${{ vars.PUBLIC_API_URL }}
          PUBLIC_S3: ${{ vars.PUBLIC_S3 }}
          PUBLIC_GITHUB_SHA: ${{ env.SHA_SHORT }}
          PUBLIC_ENVIRONMENT: ${{ vars.PUBLIC_ENVIRONMENT }}
          PUBLIC_MONTHS_TO_HISTORICAL: ${{ vars.PUBLIC_MONTHS_TO_HISTORICAL }}

      - name: Copy files to the S3 bucket
        uses: gigadat/deploy-to-s3@main
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-2
        with:
          target: build
          exclusions: (index|404).html$
          bucket: dev.fa.gigadatsolutions.com

      - name: Flush Cloudfront Cache
        run: |
          aws cloudfront create-invalidation --distribution-id E109EMBNV374Q7 --paths "/*"
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-2
