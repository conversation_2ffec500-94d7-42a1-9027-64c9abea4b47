import forms from "@tailwindcss/forms";
import typography from "@tailwindcss/typography";
import plugin from "tailwindcss/plugin";

export default {
  darkMode: "class",
  content: ["./src/**/*.{html,js,svelte,ts}"],
  theme: {
    extend: {
      colors: {
        secondary: "#25201D",
        primary: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          500: "#3b82f6",
        },
      },
      borderRadius: {
        none: "0px",
        // eslint-disable-next-line @typescript-eslint/naming-convention
        DEFAULT: "0.5rem",
        md: "0.25rem",
        lg: "1rem",
        xl: "2.25rem",
        full: "9999px",
      },
    },
  },
  plugins: [
    typography,
    forms,
    plugin(({ addBase, theme }) => {
      addBase({
        html: { color: theme("colors.gray.800") },
        h1: {
          fontSize: theme("fontSize.5xl"),
          fontWeight: theme("fontWeight.bold"),
        },
        h2: {
          fontSize: theme("fontSize.3xl"),
          fontWeight: theme("fontWeight.semibold"),
        },
        h3: {
          fontSize: theme("fontSize.2xl"),
          fontWeight: theme("fontWeight.semibold"),
        },
        h4: {
          fontSize: theme("fontSize.xl"),
          fontWeight: theme("fontWeight.semibold"),
        },
        p: {
          fontSize: theme("fontSize.base"),
          fontWeight: theme("fontWeight.normal"),
        },
        li: {
          fontSize: theme("fontSize.base"),
          fontWeight: theme("fontWeight.normal"),
        },
        small: {
          fontSize: theme("fontSize.sm"),
          fontWeight: theme("fontWeight.normal"),
          color: theme("colors.gray.500"),
        },
        a: {
          color: theme("colors.primary.500"),
          fontWeight: theme("fontWeight.semibold"),
        },
      });
    }),
  ],
};
